/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Sinhala', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    transition: all 0.3s ease;
}

body.no-scroll {
    overflow: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 35, 126, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo h2 {
    color: #ffd700;
    font-size: 1.8rem;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.nav-link:hover {
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(26, 35, 126, 0.95);
    backdrop-filter: blur(10px);
    min-width: 200px;
    list-style: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.8rem 1.2rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-menu a:hover {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 24px;
    cursor: pointer;
    z-index: 1001;
    padding: 0;
    background: transparent;
    border: none;
}

.hamburger span {
    width: 100%;
    height: 3px;
    background-color: white;
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-2px, 8px);
}

.hamburger.active span:nth-child(2) {
    transform: scale(0);
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-2px, -8px);
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a237e 0%, #3f51b5 50%, #9c27b0 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>') repeat;
    animation: starfield 20s linear infinite;
}

@keyframes starfield {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-symbol {
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
}

.floating-symbol:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-symbol:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-symbol:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.floating-symbol:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    text-align: center;
    color: white;
    z-index: 2;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffeb3b, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
    to { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(45deg, #ffd700, #ffeb3b);
    color: #1a237e;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid #ffd700;
}

.btn-secondary:hover {
    background: #ffd700;
    color: #1a237e;
    transform: translateY(-3px);
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid #ffd700;
    border-bottom: 2px solid #ffd700;
    transform: rotate(45deg);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Zodiac Section */
.zodiac-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.section-title {
    text-align: center;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a237e;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.zodiac-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.zodiac-card {
    height: 300px;
    perspective: 1000px;
    cursor: pointer;
}

.card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.zodiac-card:hover .card-inner {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-front {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
}

.card-back {
    background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
    color: white;
    transform: rotateY(180deg);
}

.zodiac-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #1a237e;
}

.zodiac-card h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1a237e;
}

.zodiac-card p {
    color: #666;
    font-size: 1rem;
}

.card-back h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.card-back p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
}

.card-link {
    background: #ffd700;
    color: #1a237e;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.card-link:hover {
    background: #ffeb3b;
    transform: translateY(-2px);
}

/* Features Section */
.features {
    padding: 5rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a237e;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
    color: white;
}

.contact .section-title {
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.contact-info h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #ffd700;
}

.contact-info p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    font-family: 'Noto Sans Sinhala', sans-serif;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: #ffd700;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ffd700;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    padding: 0.5rem 1rem;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 5px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #ffd700;
    color: #1a1a1a;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 992px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-container {
        padding: 0.8rem 1.5rem;
    }
    
    .nav-menu {
        gap: 1.5rem;
    }
    
    .zodiac-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .footer-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-container {
        position: relative;
    }
    
    .hamburger {
        display: flex;
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 10px;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(26, 35, 126, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s ease-in-out;
        backdrop-filter: blur(10px);
        padding: 2rem 0;
        height: calc(100vh - 70px);
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 1rem 0;
        width: 100%;
    }
    
    .nav-item {
        width: 100%;
    }
    
    .nav-link {
        display: block;
        padding: 0.8rem 1rem;
        width: 100%;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 0;
        visibility: hidden;
        transform: none;
        background: rgba(255, 255, 255, 0.1);
        margin-top: 0;
        box-shadow: none;
        width: 100%;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        padding: 0;
    }
    
    .dropdown-menu.active {
        opacity: 1;
        visibility: visible;
        max-height: 500px;
        padding: 0.5rem 0;
    }
    
    /* Remove hover effect on mobile */
    .dropdown:hover .dropdown-menu {
        opacity: 0;
        visibility: hidden;
        max-height: 0;
    }
    
    .dropdown:hover .dropdown-menu.active {
        opacity: 1;
        visibility: visible;
        max-height: 500px;
    }
    
    .dropdown-menu li {
        margin: 0.5rem 0;
    }
    
    .dropdown-menu a {
        padding: 0.6rem 1rem;
        color: white;
        display: block;
        width: 100%;
    }
    
    .dropdown-menu a:hover {
        background: rgba(255, 215, 0, 0.2);
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .zodiac-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .nav-container {
        padding: 0.8rem;
    }
    
    .nav-logo h2 {
        font-size: 1.5rem;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
        margin-bottom: 2rem;
    }
    
    .zodiac-card {
        height: 250px;
    }
    
    .zodiac-icon {
        font-size: 3rem;
    }
    
    .zodiac-card h3 {
        font-size: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .feature-icon {
        font-size: 2.5rem;
    }
    
    .feature-card h3 {
        font-size: 1.3rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    .contact-form input,
    .contact-form textarea {
        padding: 0.8rem;
    }
    
    .footer {
        padding: 2rem 0 1rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    border-top-color: #ffd700;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}