/* Zodiac Pages Specific Styles */

/* Zodiac Hero Section */
.zodiac-hero {
    position: relative;
    min-height: 70vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 70px; /* Account for fixed navbar */
    padding: 2rem 1rem;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.zodiac-constellation {
    position: relative;
    width: 100%;
    height: 100%;
}

.star {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #fff;
    border-radius: 50%;
    animation: twinkle 2s infinite alternate;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.star:nth-child(2n) {
    animation-delay: 0.5s;
    width: 3px;
    height: 3px;
}

.star:nth-child(3n) {
    animation-delay: 1s;
    width: 5px;
    height: 5px;
}

@keyframes twinkle {
    0% { opacity: 0.3; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.2); }
}

/* Zodiac Header */
.zodiac-header {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    padding: 2rem 1rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Zodiac Symbol */
.zodiac-symbol {
    font-size: 6rem;
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    0% { text-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
    100% { text-shadow: 0 0 30px rgba(255, 215, 0, 1), 0 0 40px rgba(255, 215, 0, 0.8); }
}

.zodiac-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.zodiac-dates {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Media queries for zodiac pages */
@media (max-width: 768px) {
    .zodiac-symbol {
        font-size: 5rem;
    }
    
    .zodiac-title {
        font-size: 2.8rem;
    }
    
    .zodiac-dates {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .zodiac-element,
    .zodiac-ruler {
        margin: 0 0.5rem 1rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .zodiac-hero {
        min-height: 60vh;
    }
    
    .zodiac-symbol {
        font-size: 4rem;
    }
    
    .zodiac-title {
        font-size: 2.2rem;
    }
    
    .zodiac-dates {
        font-size: 1rem;
        margin-bottom: 1.2rem;
    }
}

.zodiac-element,
.zodiac-ruler {
    display: inline-block;
    margin: 0 1rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.element-label,
.ruler-label {
    font-weight: 600;
    margin-right: 0.5rem;
}

.element-value,
.ruler-value {
    color: #ffd700;
    font-weight: 700;
}

/* Daily Content Section */
.daily-content {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.content-header {
    text-align: center;
    margin-bottom: 3rem;
}

.content-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.last-updated {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.refresh-btn {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.refresh-btn:active .refresh-icon {
    animation: spin 1s linear;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.daily-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.daily-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.daily-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(45deg, #007bff, #28a745, #ffc107, #dc3545);
}

.daily-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.daily-card h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
    font-weight: 600;
}

.card-content {
    color: #464a4e;
    line-height: 1.6;
    font-size: 1rem;
}

.loading-content {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 2rem 0;
}

/* Characteristics Section */
.characteristics {
    padding: 4rem 0;
    background: white;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    color: #2c3e50;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #007bff, #28a745);
    border-radius: 2px;
}

.characteristics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.characteristic-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid;
}

.characteristic-card.positive {
    border-left-color: #28a745;
}

.characteristic-card.negative {
    border-left-color: #dc3545;
}

.characteristic-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-header .card-icon {
    font-size: 2rem;
    margin-right: 1rem;
    margin-bottom: 0;
}

.card-header h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin: 0;
}

.traits-list {
    list-style: none;
    padding: 0;
}

.traits-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    position: relative;
    padding-left: 1.5rem;
}

.traits-list li:last-child {
    border-bottom: none;
}

.traits-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #007bff;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Mythology Section */
.mythology {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.mythology .section-title {
    color: white;
}

.mythology .section-title::after {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
}

.mythology-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.myth-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.myth-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.myth-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 1rem;
}

.myth-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.myth-card p {
    line-height: 1.6;
    opacity: 0.9;
}

/* Rituals Section */
.rituals {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.rituals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.ritual-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ritual-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
}

.ritual-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.ritual-icon {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.ritual-card h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    text-align: center;
}

.ritual-content p {
    margin-bottom: 0.8rem;
    line-height: 1.5;
}

.ritual-content strong {
    color: #007bff;
    font-weight: 600;
}

/* Compatibility Section */
.compatibility {
    padding: 4rem 0;
    background: white;
}

.compatibility-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.compatibility-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid;
}

.compatibility-card.high {
    border-left-color: #28a745;
}

.compatibility-card.medium {
    border-left-color: #ffc107;
}

.compatibility-card.low {
    border-left-color: #dc3545;
}

.compatibility-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.compatibility-card h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    text-align: center;
}

.zodiac-matches {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.match-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.match-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.zodiac-symbol {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.zodiac-name {
    flex: 1;
    margin-left: 1rem;
    font-weight: 500;
}

.match-percentage {
    font-weight: 700;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    color: white;
    font-size: 0.9rem;
}

.high .match-percentage {
    background: #28a745;
}

.medium .match-percentage {
    background: #ffc107;
    color: #212529;
}

.low .match-percentage {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .zodiac-symbol {
        font-size: 4rem;
    }
    
    .zodiac-title {
        font-size: 2.5rem;
    }
    
    .zodiac-dates {
        font-size: 1.1rem;
    }
    
    .zodiac-element,
    .zodiac-ruler {
        display: block;
        margin: 0.5rem 0;
    }
    
    .daily-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .characteristics-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .mythology-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .rituals-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .compatibility-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .last-updated {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .zodiac-hero {
        min-height: 60vh;
    }
    
    .zodiac-header {
        padding: 1rem;
    }
    
    .zodiac-symbol {
        font-size: 3rem;
    }
    
    .zodiac-title {
        font-size: 2rem;
    }
    
    .daily-card,
    .characteristic-card,
    .myth-card,
    .ritual-card,
    .compatibility-card {
        padding: 1.5rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Success and Error Messages */
.success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 5px;
    border: 1px solid #c3e6cb;
    margin: 1rem 0;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 5px;
    border: 1px solid #f5c6cb;
    margin: 1rem 0;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease forwards; /* Added 'forwards' to maintain the final state */
    opacity: 1;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #007bff, #28a745);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #0056b3, #1e7e34);
}