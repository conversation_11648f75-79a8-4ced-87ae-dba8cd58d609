// Configuration file for Kubera Manthra
// Handles environment variables and API settings

class Config {
    constructor() {
        this.openaiApiKey = null;
        this.loadConfig();
        
        // Log the API key source and first few characters for debugging
        console.log('API Key source:', this.apiKeySource);
        console.log('API Key (first 10 chars):', this.openaiApiKey ? this.openaiApiKey.substring(0, 10) + '...' : 'No API key');
    }

    // Load configuration from various sources
    loadConfig() {
        // Try to load from environment variables (for Node.js environments)
        if (typeof process !== 'undefined' && process.env && process.env.OPENAI_API_KEY) {
            this.openaiApiKey = process.env.OPENAI_API_KEY;
            this.apiKeySource = 'process.env';
            return;
        }
        
        // For browser environments, try to load from a global config object
        if (typeof window !== 'undefined' && window.APP_CONFIG && window.APP_CONFIG.OPENAI_API_KEY) {
            this.openaiApiKey = window.APP_CONFIG.OPENAI_API_KEY;
            this.apiKeySource = 'window.APP_CONFIG';
            return;
        }
        
        // For browser environments, try to load from a global variable set by env-loader script
        if (typeof window !== 'undefined' && window.ENV && window.ENV.OPENAI_API_KEY) {
            this.openaiApiKey = window.ENV.OPENAI_API_KEY;
            this.apiKeySource = 'window.ENV';
            return;
        }
        
        // Try to load from localStorage (for development)
        if (typeof localStorage !== 'undefined' && localStorage.getItem('OPENAI_API_KEY')) {
            this.openaiApiKey = localStorage.getItem('OPENAI_API_KEY');
            this.apiKeySource = 'localStorage';
            return;
        }
        
        // If no API key is found, set a default source
        this.apiKeySource = 'none';
    }

    // Get OpenAI API configuration
    getOpenAIConfig() {
        return {
            apiKey: this.openaiApiKey || 'sk-your_openai_api_key_here', // Fallback to a placeholder
            baseUrl: 'https://api.openai.com/v1/chat/completions',
            model: 'gpt-3.5-turbo', // Using GPT-3.5-turbo for cost efficiency
            maxRetries: 3,
            retryDelay: 2000,
            timeout: 30000,
            rateLimit: {
                requestsPerMinute: 20, // OpenAI allows higher rate limits
                delayBetweenRequests: 3000 // 3 seconds between requests
            }
        };
    }

    // Check if API key is configured and valid
    isApiKeyConfigured() {
        if (!this.openaiApiKey || typeof this.openaiApiKey !== 'string') {
            console.log('isApiKeyConfigured: No API key found');
            return false;
        }

        // Check if it's a placeholder
        const placeholders = [
            'YOUR_OPENAI_API_KEY_HERE',
            'sk-your_openai_api_key_here',
            '********************************************************************************************************************************************************************'
        ];

        if (placeholders.includes(this.openaiApiKey)) {
            console.log('isApiKeyConfigured: Placeholder API key detected');
            return false;
        }

        // Check basic format
        const trimmedKey = this.openaiApiKey.trim();
        const isValidFormat = trimmedKey.startsWith('sk-') && trimmedKey.length >= 20;

        console.log('isApiKeyConfigured:', isValidFormat);
        console.log('API Key source:', this.apiKeySource);
        console.log('API Key format valid:', isValidFormat);

        return isValidFormat;
    }

    // Get API key validation status with detailed info
    getApiKeyStatus() {
        const isConfigured = this.isApiKeyConfigured();

        return {
            isConfigured,
            source: this.apiKeySource,
            hasKey: !!this.openaiApiKey,
            keyLength: this.openaiApiKey ? this.openaiApiKey.length : 0,
            isPlaceholder: this.openaiApiKey && [
                'YOUR_OPENAI_API_KEY_HERE',
                'sk-your_openai_api_key_here'
            ].includes(this.openaiApiKey),
            message: isConfigured ?
                'API key is configured and valid' :
                'API key is missing or invalid. Please configure your OpenAI API key.'
        };
    }

    // Set API key (for development/testing)
    setApiKey(apiKey) {
        this.openaiApiKey = apiKey;
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('OPENAI_API_KEY', apiKey);
        }
    }

    // Get enhanced prompts for better content generation
    getPrompts(zodiacSign, zodiacConfig) {
        const today = new Date();
        const dateStr = today.toLocaleDateString('si-LK', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        
        const baseContext = `ඔබ ශ්‍රී ලාංකික ජ්‍යෝතිෂ විශේෂඥයෙකු වන අතර ${zodiacConfig.name} ලග්නය පිළිබඳ ගැඹුරු දැනුමක් ඇත. ${dateStr} දිනය සඳහා`;
        
        return {
            dailyPrediction: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට විශේෂ දෛනික පුරෝකථනයක් ලියන්න. ග්‍රහ ස්ථාන, ශුභාශුභ කාල, සහ ප්‍රායෝගික උපදෙස් ඇතුළත් කරන්න. 150-200 වචන භාවිතා කරන්න.`,
            
            wealthForecast: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ගේ ධන සම්පත්, ව්‍යාපාරික අවස්ථා, ආයෝජන සහ මූල්‍ය තීරණ පිළිබඳ විස්තරාත්මක පුරෝකථනයක් ලියන්න. නිශ්චිත උපදෙස් සහ අවවාද ඇතුළත් කරන්න.`,
            
            loveForecast: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ගේ ප්‍රේම සම්බන්ධතා, විවාහ ජීවිතය, පවුල් සාමය, සහ සමාජ සම්බන්ධතා පිළිබඳ සවිස්තරාත්මක පුරෝකථනයක් ලියන්න. ප්‍රායෝගික ප්‍රේම උපදෙස් ඇතුළත් කරන්න.`,
            
            healthForecast: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ගේ සෞඛ්‍ය තත්වය, ශාරීරික යහපැවැත්ම, මානසික සුවතාව, සහ ආහාර පාන පිළිබඳ විස්තරාත්මක පුරෝකථනයක් ලියන්න. සෞඛ්‍ය ප්‍රතිපත්ති සහ අවවාද ඇතුළත් කරන්න.`,
            
            auspiciousTime: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට ශුභ කාල වේලාවන්, වැදගත් කටයුතු සඳහා සුදුසු වේලාවන්, සහ වර්ජනීය කාල පිළිබඳ නිශ්චිත තොරතුරු ලබා දෙන්න. කාල වේලාවන් පැය සහ මිනිත්තු වලින් සඳහන් කරන්න.`,
            
            luckyColor: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට ශුභ වර්ණ, ඒවා භාවිතා කළ යුතු ආකාරය, ඇඳුම් පැළඳුම්, ගෘහ අලංකරණය, සහ වර්ණ ශක්තිය පිළිබඳ සවිස්තරාත්මක මාර්ගෝපදේශ ලබා දෙන්න.`
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Config;
} else {
    window.Config = Config;
}