/**
 * Configuration file for Kubera Manthra
 * This file is kept for backward compatibility
 * The actual configuration is now handled by environment variables and API endpoints
 */

console.warn('config.js is deprecated. Configuration is now handled server-side.');

// Backward compatibility object
window.CONFIG = {
    API_BASE_URL: '',
    SERVICE_NAME: 'Kubera Manthra',
    VERSION: '2.0.0',
    FEATURES: {
        GEMINI_AI: true,
        CACHING: true,
        FALLBACK_CONTENT: true,
        OFFLINE_SUPPORT: true
    },
    ENDPOINTS: {
        HEALTH: '/api/health',
        ZODIAC_SIGNS: '/api/zodiac-signs',
        DAILY_HOROSCOPE: '/api/daily-horoscope',
        CLEAR_CACHE: '/api/clear-cache'
    },
    CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours
    REQUEST_TIMEOUT: 15000, // 15 seconds
    RETRY_ATTEMPTS: 2
};

// Log configuration load
console.log('Configuration loaded (compatibility mode):', window.CONFIG);

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.CONFIG;
}
