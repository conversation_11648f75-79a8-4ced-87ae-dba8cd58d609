#!/usr/bin/env node

/**
 * Setup script for configuring OpenAI API key
 * Run with: node setup-api-key.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return { valid: false, message: 'API key is required' };
    }
    
    const trimmed = apiKey.trim();
    
    if (trimmed === 'YOUR_OPENAI_API_KEY_HERE') {
        return { valid: false, message: 'Please replace with your actual API key' };
    }
    
    if (!trimmed.startsWith('sk-')) {
        return { valid: false, message: 'API key should start with "sk-"' };
    }
    
    if (trimmed.length < 20) {
        return { valid: false, message: 'API key appears to be too short' };
    }
    
    return { valid: true, message: 'API key format looks valid' };
}

function updateEnvConfig(api<PERSON><PERSON>) {
    const envConfigPath = path.join(__dirname, 'scripts', 'env-config.js');
    
    try {
        let content = fs.readFileSync(envConfigPath, 'utf8');
        
        // Replace the API key line
        content = content.replace(
            /const OPENAI_API_KEY = ['"](.*?)['"];/,
            `const OPENAI_API_KEY = '${apiKey}';`
        );
        
        fs.writeFileSync(envConfigPath, content, 'utf8');
        return true;
    } catch (error) {
        console.error('Error updating env-config.js:', error.message);
        return false;
    }
}

async function testApiKey(apiKey) {
    console.log('\n🧪 Testing API key...');
    
    try {
        const fetch = require('node-fetch');
        
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [{
                    role: 'user',
                    content: 'Say "Hello" in Sinhala'
                }],
                max_tokens: 10
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API key test successful!');
            console.log('Response:', data.choices[0].message.content);
            return true;
        } else {
            const errorText = await response.text();
            console.log('❌ API key test failed:');
            console.log('Status:', response.status);
            console.log('Error:', errorText);
            return false;
        }
    } catch (error) {
        console.log('❌ API key test failed:', error.message);
        return false;
    }
}

async function main() {
    console.log('🔑 Kubera Manthra - OpenAI API Key Setup\n');
    console.log('This script will help you configure your OpenAI API key.\n');
    console.log('Steps to get your API key:');
    console.log('1. Visit: https://platform.openai.com/api-keys');
    console.log('2. Sign in to your OpenAI account');
    console.log('3. Click "Create new secret key"');
    console.log('4. Copy the generated key\n');
    
    rl.question('Enter your OpenAI API key: ', async (apiKey) => {
        const validation = validateApiKey(apiKey);
        
        if (!validation.valid) {
            console.log(`❌ ${validation.message}`);
            rl.close();
            return;
        }
        
        console.log(`✅ ${validation.message}`);
        
        // Test the API key
        const testResult = await testApiKey(apiKey.trim());
        
        if (!testResult) {
            console.log('\n⚠️  API key test failed. Do you still want to save it? (y/N)');
            rl.question('', (answer) => {
                if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                    if (updateEnvConfig(apiKey.trim())) {
                        console.log('✅ API key saved to env-config.js');
                        console.log('🔄 Please restart the server to apply changes');
                    }
                } else {
                    console.log('❌ Setup cancelled');
                }
                rl.close();
            });
        } else {
            // Save the working API key
            if (updateEnvConfig(apiKey.trim())) {
                console.log('✅ API key saved to env-config.js');
                console.log('🔄 Please restart the server to apply changes');
                console.log('\n🎉 Setup complete! Your app is now ready to use AI features.');
            }
            rl.close();
        }
    });
}

main().catch(console.error);
