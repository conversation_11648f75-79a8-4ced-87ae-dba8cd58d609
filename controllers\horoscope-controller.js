/**
 * Horoscope Controller for Kubera Manthra
 * Handles all horoscope-related API endpoints
 */

const GeminiService = require('../services/gemini-service');

class HoroscopeController {
    constructor() {
        this.geminiService = new GeminiService();
        this.zodiacConfig = this.initializeZodiacConfig();
    }

    /**
     * Initialize zodiac configuration
     * @returns {Object}
     */
    initializeZodiacConfig() {
        return {
            mesha: {
                name: 'මේෂ',
                symbol: '♈',
                element: 'ගින්න',
                ruler: 'අඟහරු',
                dates: 'මාර්තු 21 - අප්‍රේල් 19',
                color: '#FF4444',
                gemstone: 'රතු කොරල්',
                luckyNumbers: [1, 8, 17],
                keywords: ['නායකත්වය', 'ධෛර්යය', 'ක්‍රියාශීලිත්වය', 'නවෝත්පාදනය']
            },
            vrishabha: {
                name: 'වෘෂභ',
                symbol: '♉',
                element: 'පෘථිවි',
                ruler: 'සිකුරු',
                dates: 'අප්‍රේල් 20 - මැයි 20',
                color: '#4CAF50',
                gemstone: 'මරකත',
                luckyNumbers: [2, 6, 9],
                keywords: ['ස්ථාවරත්වය', 'විශ්වසනීයත්වය', 'ඉවසීම', 'ප්‍රායෝගිකත්වය']
            },
            mithuna: {
                name: 'මිථුන',
                symbol: '♊',
                element: 'වායු',
                ruler: 'බුධ',
                dates: 'මැයි 21 - ජූනි 20',
                color: '#FFD700',
                gemstone: 'පන්නා',
                luckyNumbers: [5, 7, 14],
                keywords: ['සන්නිවේදනය', 'බුද්ධිමත්', 'අනුවර්තනය', 'කුතුහලය']
            },
            karkata: {
                name: 'කර්කට',
                symbol: '♋',
                element: 'ජලය',
                ruler: 'චන්ද්‍රයා',
                dates: 'ජූනි 21 - ජූලි 22',
                color: '#87CEEB',
                gemstone: 'මුතු',
                luckyNumbers: [2, 7, 11],
                keywords: ['සංවේදනය', 'රැකවරණය', 'අන්තර්ගත බුද්ධිය', 'පවුල්කාමිත්වය']
            },
            simha: {
                name: 'සිංහ',
                symbol: '♌',
                element: 'ගින්න',
                ruler: 'සූර්යයා',
                dates: 'ජූලි 23 - අගෝස්තු 22',
                color: '#FF8C00',
                gemstone: 'රුබි',
                luckyNumbers: [1, 3, 10],
                keywords: ['නායකත්වය', 'ආත්මවිශ්වාසය', 'නිර්මාණශීලිත්වය', 'ත්‍යාගශීලිත්වය']
            },
            kanya: {
                name: 'කන්‍යා',
                symbol: '♍',
                element: 'පෘථිවි',
                ruler: 'බුධ',
                dates: 'අගෝස්තු 23 - සැප්තැම්බර් 22',
                color: '#8FBC8F',
                gemstone: 'නීලමණි',
                luckyNumbers: [6, 14, 18],
                keywords: ['විශ්ලේෂණාත්මක', 'පරිපූර්ණතාවාදී', 'ප්‍රායෝගික', 'සේවාශීලී']
            },
            tula: {
                name: 'තුලා',
                symbol: '♎',
                element: 'වායු',
                ruler: 'සිකුරු',
                dates: 'සැප්තැම්බර් 23 - ඔක්තෝබර් 22',
                color: '#FFB6C1',
                gemstone: 'ඔපල්',
                luckyNumbers: [4, 6, 15],
                keywords: ['සමතුලිතතාවය', 'සාධාරණත්වය', 'සමගිය', 'රූපලාවණ්‍යය']
            },
            vrischika: {
                name: 'වෘශ්චික',
                symbol: '♏',
                element: 'ජලය',
                ruler: 'අඟහරු',
                dates: 'ඔක්තෝබර් 23 - නොවැම්බර් 21',
                color: '#8B0000',
                gemstone: 'ටොපාස්',
                luckyNumbers: [8, 11, 18],
                keywords: ['තීව්‍රතාවය', 'පරිවර්තනය', 'අභිරහස්', 'දෘඪත්වය']
            },
            dhanu: {
                name: 'ධනු',
                symbol: '♐',
                element: 'ගින්න',
                ruler: 'ගුරු',
                dates: 'නොවැම්බර් 22 - දෙසැම්බර් 21',
                color: '#9370DB',
                gemstone: 'ටර්කොයිස්',
                luckyNumbers: [3, 9, 22],
                keywords: ['ගවේෂණය', 'ප්‍රශස්තතාවය', 'දර්ශනය', 'ස්වාධීනත්වය']
            },
            makara: {
                name: 'මකර',
                symbol: '♑',
                element: 'පෘථිවි',
                ruler: 'සෙනසුරු',
                dates: 'දෙසැම්බර් 22 - ජනවාරි 19',
                color: '#2F4F4F',
                gemstone: 'ගාර්නට්',
                luckyNumbers: [6, 9, 26],
                keywords: ['අභිලාෂය', 'විනය', 'වගකීම', 'ප්‍රායෝගිකත්වය']
            },
            kumbha: {
                name: 'කුම්භ',
                symbol: '♒',
                element: 'වායු',
                ruler: 'සෙනසුරු',
                dates: 'ජනවාරි 20 - පෙබරවාරි 18',
                color: '#00CED1',
                gemstone: 'ඇමතිස්ට්',
                luckyNumbers: [4, 7, 11],
                keywords: ['නවෝත්පාදනය', 'මානවිකත්වය', 'ස්වාධීනත්වය', 'අනාගතවාදය']
            },
            meena: {
                name: 'මීන',
                symbol: '♓',
                element: 'ජලය',
                ruler: 'ගුරු',
                dates: 'පෙබරවාරි 19 - මාර්තු 20',
                color: '#20B2AA',
                gemstone: 'ඇක්වාමරීන්',
                luckyNumbers: [3, 9, 12],
                keywords: ['සංවේදනය', 'අන්තර්ගත බුද්ධිය', 'කරුණාව', 'කලාත්මකත්වය']
            }
        };
    }

    /**
     * Get daily horoscope for a specific zodiac sign
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async getDailyHoroscope(req, res) {
        const startTime = Date.now();
        
        try {
            const { zodiacSign } = req.params;
            
            // Validate zodiac sign
            if (!zodiacSign || !this.zodiacConfig[zodiacSign]) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid zodiac sign',
                    availableSigns: Object.keys(this.zodiacConfig)
                });
            }

            const zodiacConfig = this.zodiacConfig[zodiacSign];
            
            console.log(`[${new Date().toISOString()}] Generating horoscope for ${zodiacSign}`);

            // Generate content using Gemini service
            const content = await this.geminiService.generateDailyContent(zodiacSign, zodiacConfig);

            const responseTime = Date.now() - startTime;

            // Add metadata
            const response = {
                success: true,
                zodiacSign,
                zodiacConfig: {
                    name: zodiacConfig.name,
                    symbol: zodiacConfig.symbol,
                    element: zodiacConfig.element,
                    ruler: zodiacConfig.ruler,
                    dates: zodiacConfig.dates,
                    color: zodiacConfig.color,
                    gemstone: zodiacConfig.gemstone,
                    luckyNumbers: zodiacConfig.luckyNumbers
                },
                content,
                metadata: {
                    generatedAt: new Date().toISOString(),
                    responseTime: `${responseTime}ms`,
                    serviceAvailable: this.geminiService.isAvailable(),
                    cached: false // This would be determined by the service
                }
            };

            console.log(`[${new Date().toISOString()}] Horoscope generated successfully for ${zodiacSign} (${responseTime}ms)`);
            
            res.json(response);

        } catch (error) {
            const responseTime = Date.now() - startTime;
            console.error(`[${new Date().toISOString()}] Error generating horoscope (${responseTime}ms):`, error.message);
            
            res.status(500).json({
                success: false,
                error: 'Failed to generate horoscope',
                message: error.message,
                metadata: {
                    responseTime: `${responseTime}ms`,
                    timestamp: new Date().toISOString()
                }
            });
        }
    }

    /**
     * Get all zodiac signs configuration
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async getZodiacSigns(req, res) {
        try {
            const signs = Object.keys(this.zodiacConfig).map(key => ({
                key,
                ...this.zodiacConfig[key]
            }));

            res.json({
                success: true,
                signs,
                total: signs.length
            });
        } catch (error) {
            console.error('Error getting zodiac signs:', error.message);
            res.status(500).json({
                success: false,
                error: 'Failed to get zodiac signs',
                message: error.message
            });
        }
    }

    /**
     * Health check endpoint
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async healthCheck(req, res) {
        try {
            const stats = this.geminiService.getStats();

            res.json({
                success: true,
                status: 'healthy',
                service: {
                    ...stats,
                    uptime: process.uptime(),
                    timestamp: new Date().toISOString()
                }
            });
        } catch (error) {
            console.error('Health check error:', error.message);
            res.status(500).json({
                success: false,
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Clear cache endpoint (for maintenance)
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async clearCache(req, res) {
        try {
            this.geminiService.clearCache();

            res.json({
                success: true,
                message: 'Cache cleared successfully',
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Clear cache error:', error.message);
            res.status(500).json({
                success: false,
                error: 'Failed to clear cache',
                message: error.message
            });
        }
    }
}

module.exports = HoroscopeController;
