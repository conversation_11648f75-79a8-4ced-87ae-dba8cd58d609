# Kubera Manthra - Setup Guide

## OpenAI API Configuration

To enable real-time daily content generation for zodiac predictions, you need to configure the OpenAI API.

### Step 1: Get Your OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### Step 2: Configure the API Key

You have several options to configure your API key:

#### Option 1: Environment File (.env)
1. Open the `.env` file in the project root
2. Replace `sk-your_openai_api_key_here` with your actual API key:
```
OPENAI_API_KEY=sk-your_actual_api_key_here
   ```
3. Save the file

#### Option 2: Browser Local Storage (Development)
1. Open your browser's developer console (F12)
2. Go to the Application/Storage tab
3. Find Local Storage for your domain
4. Add a new key-value pair:
   - Key: `OPENAI_API_KEY`
   - Value: Your API key

#### Option 3: Global Configuration (Advanced)
1. Add this to your HTML head section or before loading scripts:
   ```html
   <script>
   window.APP_CONFIG = {
       OPENAI_API_KEY: 'sk-your_actual_api_key_here'
   };
   </script>
   ```

### Step 3: Test the Configuration

1. Start your development server:
   ```bash
   npm start
   ```

2. Navigate to any zodiac page (e.g., http://localhost:8000/pages/mesha.html)

3. Check the browser console for any API-related messages

4. Click the "Refresh" button to test real-time content generation

### Features Enabled with API Configuration

✅ **Real-time Daily Predictions**: Fresh content generated daily
✅ **Detailed Forecasts**: Wealth, love, health predictions
✅ **Auspicious Timings**: Specific time recommendations
✅ **Lucky Colors**: Daily color guidance
✅ **Enhanced Content**: More detailed and personalized predictions

### Fallback Content

If the API key is not configured or there are connectivity issues, the website will automatically use fallback content to ensure a smooth user experience.

### API Usage and Limits

- **Model**: GPT-3.5-turbo (cost-effective and efficient)
- **Rate Limiting**: 15 requests per minute with 4-second delays
- **Content Quality**: Enhanced prompts for better Sinhala content
- **Safety**: Built-in content filtering and error handling

### Troubleshooting

#### API Key Issues
- Ensure your API key is valid and active
- Check that you have sufficient quota in your OpenAI account
- Verify the key is properly formatted (starts with "sk-")

#### Network Issues
- Check your internet connection
- Ensure your firewall allows requests to `api.openai.com`
- Try refreshing the page if requests timeout

#### Console Errors
- Open browser developer tools (F12)
- Check the Console tab for error messages
- Look for network errors in the Network tab

### Security Notes

⚠️ **Important**: Never commit your API key to version control
⚠️ **Production**: Use environment variables or secure configuration management
⚠️ **Client-side**: API keys are visible in browser - consider server-side proxy for production

### Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your API key configuration
3. Test with a simple zodiac page first
4. Ensure you have a stable internet connection

For additional help, refer to the [OpenAI API documentation](https://platform.openai.com/docs/api-reference).