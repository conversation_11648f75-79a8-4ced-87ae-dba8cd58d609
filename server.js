const express = require('express');
const cors = require('cors');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Serve static files from the current directory
app.use(express.static('.'));

// Validate API key format
function isValidApiKeyFormat(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return false;
    }

    // Check basic format: starts with 'sk-' and has reasonable length
    const trimmed = apiKey.trim();
    return trimmed.startsWith('sk-') && trimmed.length >= 20;
}

// Proxy endpoint for OpenAI API with improved error handling
app.post('/api/openai', async (req, res) => {
    const startTime = Date.now();

    try {
        const { apiKey, requestBody } = req.body;

        // Log request details (without full API key for security)
        console.log(`[${new Date().toISOString()}] OpenAI API Request:`);
        console.log('- API Key provided:', !!apiKey);
        console.log('- API Key format valid:', isValidApiKeyFormat(apiKey));
        console.log('- API Key (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'None');
        console.log('- Model:', requestBody?.model || 'Not specified');
        console.log('- Messages count:', requestBody?.messages?.length || 0);

        // Validate API key
        if (!apiKey) {
            console.error('- Error: No API key provided');
            return res.status(400).json({
                error: 'API key is required. Please configure your OpenAI API key.'
            });
        }

        if (!isValidApiKeyFormat(apiKey)) {
            console.error('- Error: Invalid API key format');
            return res.status(400).json({
                error: 'Invalid API key format. API key should start with "sk-" and be at least 20 characters long.'
            });
        }

        // Validate request body
        if (!requestBody || !requestBody.model || !requestBody.messages) {
            console.error('- Error: Invalid request body');
            return res.status(400).json({
                error: 'Invalid request body. Model and messages are required.'
            });
        }

        const apiUrl = 'https://api.openai.com/v1/chat/completions';
        console.log('- Sending request to OpenAI API...');

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(requestBody)
        });

        const responseTime = Date.now() - startTime;
        console.log(`- Response status: ${response.status}`);
        console.log(`- Response time: ${responseTime}ms`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('- OpenAI API Error:', errorText);

            // Parse error for better user feedback
            let errorMessage = `API request failed: ${response.status}`;
            try {
                const errorData = JSON.parse(errorText);
                if (errorData.error && errorData.error.message) {
                    errorMessage = errorData.error.message;
                }
            } catch (parseError) {
                // Use default message if parsing fails
            }

            return res.status(response.status).json({
                error: errorMessage,
                status: response.status,
                timestamp: new Date().toISOString()
            });
        }

        const data = await response.json();
        console.log('- Success: Response received');
        console.log(`- Tokens used: ${data.usage?.total_tokens || 'Unknown'}`);

        res.json(data);

    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error(`[${new Date().toISOString()}] Proxy error (${responseTime}ms):`, error.message);
        console.error('- Stack trace:', error.stack);

        res.status(500).json({
            error: 'Internal server error. Please try again later.',
            timestamp: new Date().toISOString()
        });
    }
});

// Test endpoint to verify API key configuration
app.get('/api/test-key', async (req, res) => {
    const startTime = Date.now();

    try {
        console.log(`[${new Date().toISOString()}] API Key Test Request`);

        // Get the API key from env-config.js
        const fs = require('fs');
        const path = require('path');

        // Read the env-config.js file to extract the API key
        const envConfigPath = path.join(__dirname, 'scripts', 'env-config.js');

        if (!fs.existsSync(envConfigPath)) {
            console.error('- Error: env-config.js file not found');
            return res.status(500).json({
                error: 'Configuration file not found',
                success: false
            });
        }

        const envConfigContent = fs.readFileSync(envConfigPath, 'utf8');

        // Extract the API key using regex
        const apiKeyMatch = envConfigContent.match(/const OPENAI_API_KEY = ['"](.*?)['"];/);
        const apiKey = apiKeyMatch ? apiKeyMatch[1] : null;

        console.log('- API Key found:', !!apiKey);
        console.log('- API Key format valid:', isValidApiKeyFormat(apiKey));
        console.log('- API Key (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'None');

        if (!apiKey) {
            console.log('- Result: No API key configured');
            return res.json({
                success: false,
                configured: false,
                message: 'API key not found in env-config.js. Please configure your OpenAI API key.',
                instructions: 'Replace YOUR_OPENAI_API_KEY_HERE with your actual API key from https://platform.openai.com/api-keys'
            });
        }

        if (!isValidApiKeyFormat(apiKey)) {
            console.log('- Result: Invalid API key format');
            return res.json({
                success: false,
                configured: false,
                message: 'Invalid API key format detected. Please check your API key configuration.',
                instructions: 'API key should start with "sk-" and be at least 20 characters long'
            });
        }

        // Test the API key with OpenAI API
        console.log('- Testing API key with OpenAI API...');

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [{
                    role: 'user',
                    content: 'Say hello in Sinhala'
                }],
                temperature: 0.7,
                max_tokens: 50,
                top_p: 0.95
            })
        });

        const responseTime = Date.now() - startTime;
        console.log(`- Response status: ${response.status}`);
        console.log(`- Response time: ${responseTime}ms`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('- API Error:', errorText);

            let errorMessage = 'API key test failed';
            try {
                const errorData = JSON.parse(errorText);
                if (errorData.error && errorData.error.message) {
                    errorMessage = errorData.error.message;
                }
            } catch (parseError) {
                errorMessage = `HTTP ${response.status}: ${errorText}`;
            }

            return res.json({
                success: false,
                configured: true,
                valid: false,
                message: errorMessage,
                status: response.status,
                responseTime: responseTime
            });
        }

        const data = await response.json();
        console.log('- Success: API key is valid');
        console.log(`- Tokens used: ${data.usage?.total_tokens || 'Unknown'}`);

        res.json({
            success: true,
            configured: true,
            valid: true,
            message: 'API key is valid and working',
            response: data.choices?.[0]?.message?.content || 'Test successful',
            usage: data.usage,
            responseTime: responseTime
        });

    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error(`[${new Date().toISOString()}] Test key error (${responseTime}ms):`, error.message);

        res.status(500).json({
            success: false,
            error: 'Internal server error during API key test',
            message: error.message,
            responseTime: responseTime
        });
    }
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});