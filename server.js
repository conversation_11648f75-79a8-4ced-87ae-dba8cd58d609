const express = require('express');
const cors = require('cors');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Serve static files from the current directory
app.use(express.static('.'));

// Proxy endpoint for OpenAI API
// Add more detailed logging to the server.js file
app.post('/api/openai', async (req, res) => {
    try {
        const { apiKey, requestBody } = req.body;
        
        console.log('API Key received (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'No API key');
        console.log('Full API Key received:', apiKey); // Log the full API key for debugging
        console.log('Request body:', JSON.stringify(requestBody));
        
        if (!apiKey) {
            return res.status(400).json({ error: 'API key is required' });
        }
        
        const apiUrl = 'https://api.openai.com/v1/chat/completions';
        
        console.log('Sending request to OpenAI API...');
        
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify(requestBody)
        });
        
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error:', errorText);
            return res.status(response.status).json({ 
                error: `API request failed: ${response.status} - ${errorText}` 
            });
        }
        
        const data = await response.json();
        res.json(data);
        
    } catch (error) {
        console.error('Proxy error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Add a test endpoint to verify the API key
app.get('/api/test-key', async (req, res) => {
    try {
        // Get the API key from env-config.js
        const fs = require('fs');
        const path = require('path');
        
        // Read the env-config.js file to extract the API key
        const envConfigPath = path.join(__dirname, 'scripts', 'env-config.js');
        const envConfigContent = fs.readFileSync(envConfigPath, 'utf8');
        
        // Extract the API key using regex
        const apiKeyMatch = envConfigContent.match(/const OPENAI_API_KEY = ['"](.*?)['"];/);
        const apiKey = apiKeyMatch ? apiKeyMatch[1] : null;
        
        console.log('API Key from env-config.js (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'No API key');
        console.log('Full API Key from env-config.js:', apiKey);
        
        if (!apiKey) {
            return res.status(400).json({ error: 'API key not found in env-config.js' });
        }
        
        // Test the API key with OpenAI API
        const apiUrl = 'https://api.openai.com/v1/chat/completions';
        
        console.log('Testing API key with OpenAI API...');
        
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [{
                    role: 'user',
                    content: 'Say hello in Sinhala'
                }],
                temperature: 0.7,
                max_tokens: 50,
                top_p: 0.95
            })
        });
        
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error:', errorText);
            return res.status(response.status).json({ 
                error: `API request failed: ${response.status} - ${errorText}` 
            });
        }
        
        const data = await response.json();
        res.json({
            success: true,
            message: 'API key is valid',
            response: data
        });
        
    } catch (error) {
        console.error('Test key error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});