<!DOCTYPE html>
<html lang="si">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>මේෂ ලග්නය - කුබේර මන්ත්‍රය</title>
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/zodiac.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Sinhala:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html"><h2>කුබේර මන්ත්‍රය</h2></a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">මුල් පිටුව</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link active">ලග්න</a>
                    <ul class="dropdown-menu">
                        <li><a href="mesha.html" class="active">මේෂ</a></li>
                        <li><a href="vrishabha.html">වෘෂභ</a></li>
                        <li><a href="mithuna.html">මිථුන</a></li>
                        <li><a href="karkata.html">කටක</a></li>
                        <li><a href="simha.html">සිංහ</a></li>
                        <li><a href="kanya.html">කන්‍යා</a></li>
                        <li><a href="tula.html">තුලා</a></li>
                        <li><a href="vrischika.html">වෘශ්චික</a></li>
                        <li><a href="dhanu.html">ධනු</a></li>
                        <li><a href="makara.html">මකර</a></li>
                        <li><a href="kumbha.html">කුම්භ</a></li>
                        <li><a href="meena.html">මීන</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="../store.html" class="nav-link">වෙළඳසැල</a>
                </li>
                <li class="nav-item">
                    <a href="../index.html#contact" class="nav-link">සම්බන්ධතා</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Zodiac Hero Section -->
    <section class="zodiac-hero">
        <div class="hero-background">
            <div class="zodiac-constellation">
                <div class="star" style="top: 20%; left: 15%;"></div>
                <div class="star" style="top: 30%; left: 25%;"></div>
                <div class="star" style="top: 40%; left: 35%;"></div>
                <div class="star" style="top: 25%; left: 45%;"></div>
                <div class="star" style="top: 35%; left: 55%;"></div>
                <div class="star" style="top: 45%; left: 65%;"></div>
                <div class="star" style="top: 30%; left: 75%;"></div>
            </div>
        </div>
        <div class="container">
            <div class="zodiac-header">
                <div class="zodiac-symbol">♈</div>
                <h1 class="zodiac-title">මේෂ ලග්නය</h1>
                <p class="zodiac-dates">මාර්තු 21 - අප්‍රේල් 19</p>
                <div class="zodiac-element">
                    <span class="element-label">මූලද්‍රව්‍යය:</span>
                    <span class="element-value">ගින්න 🔥</span>
                </div>
                <div class="zodiac-ruler">
                    <span class="ruler-label">පාලක ග්‍රහයා:</span>
                    <span class="ruler-value">අඟහරු ♂</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Daily Content Section -->
    <section class="daily-content">
        <div class="container">
            <div class="content-header">
                <h2>අද දිනයේ මේෂ ලග්නය</h2>
                <div class="last-updated">
                    <span id="lastUpdated">අවසන් වරට යාවත්කාලීන කළේ: <span id="updateTime">Loading...</span></span>
                    <button class="refresh-btn" onclick="refreshDailyContent()" id="refreshBtn">
                        <span class="refresh-icon">🔄</span> යාවත්කාලීන කරන්න
                    </button>
                </div>
            </div>
            
            <div class="daily-grid">
                <div class="daily-card">
                    <div class="card-icon">🌟</div>
                    <h3>දෛනික පුරෝකථනය</h3>
                    <div class="card-content" id="dailyPrediction">
                        <div class="loading-content">Loading...</div>
                    </div>
                </div>
                
                <div class="daily-card">
                    <div class="card-icon">💰</div>
                    <h3>ධන සම්පත්</h3>
                    <div class="card-content" id="wealthForecast">
                        <div class="loading-content">Loading...</div>
                    </div>
                </div>
                
                <div class="daily-card">
                    <div class="card-icon">❤️</div>
                    <h3>ප්‍රේම සම්බන්ධතා</h3>
                    <div class="card-content" id="loveForecast">
                        <div class="loading-content">Loading...</div>
                    </div>
                </div>
                
                <div class="daily-card">
                    <div class="card-icon">🏥</div>
                    <h3>සෞඛ්‍ය තත්වය</h3>
                    <div class="card-content" id="healthForecast">
                        <div class="loading-content">Loading...</div>
                    </div>
                </div>
                
                <div class="daily-card">
                    <div class="card-icon">⏰</div>
                    <h3>ශුභ කාලය</h3>
                    <div class="card-content" id="auspiciousTime">
                        <div class="loading-content">Loading...</div>
                    </div>
                </div>
                
                <div class="daily-card">
                    <div class="card-icon">🎨</div>
                    <h3>ශුභ වර්ණ</h3>
                    <div class="card-content" id="luckyColor">
                        <div class="loading-content">Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Characteristics Section -->
    <section class="characteristics">
        <div class="container">
            <h2 class="section-title">මේෂ ලග්නයේ ගුණාංග</h2>
            
            <div class="characteristics-grid">
                <div class="characteristic-card positive">
                    <div class="card-header">
                        <div class="card-icon">✨</div>
                        <h3>ධනාත්මක ගුණාංග</h3>
                    </div>
                    <ul class="traits-list">
                        <li>නායකත්ව ගුණාංග</li>
                        <li>ධෛර්යය සහ නිර්භීතකම</li>
                        <li>ක්‍රියාශීලිත්වය</li>
                        <li>නවෝත්පාදනය</li>
                        <li>ස්වාධීනත්වය</li>
                        <li>අභිලාෂකාමිත්වය</li>
                        <li>ශක්තිමත් කැපවීම</li>
                        <li>ඉක්මන් තීරණ ගැනීම</li>
                    </ul>
                </div>
                
                <div class="characteristic-card negative">
                    <div class="card-header">
                        <div class="card-icon">⚠️</div>
                        <h3>අභියෝග</h3>
                    </div>
                    <ul class="traits-list">
                        <li>ඉක්මන් කෝපය</li>
                        <li>ඉවසීමේ අඩුව</li>
                        <li>ආවේගශීලිත්වය</li>
                        <li>ස්වාර්ථකාමිත්වය</li>
                        <li>අධික තරඟකාමිත්වය</li>
                        <li>අනෙකුන්ගේ අදහස් නොසලකා හැරීම</li>
                        <li>ප්‍රවේශම් වීම</li>
                        <li>අවධානය නොයොමු වීම</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Mythology Section -->
    <section class="mythology">
        <div class="container">
            <h2 class="section-title">පුරාණ කථා සහ සංකේතවාදය</h2>
            
            <div class="mythology-content">
                <div class="myth-card">
                    <div class="myth-icon">🐏</div>
                    <h3>මේෂ ලග්නයේ මූලාරම්භය</h3>
                <p>මේෂ ලග්නය ග්‍රීක පුරාණයේ රන්වන් බැටළුවා (Golden Ram) සමඟ සම්බන්ධ වේ. මෙම බැටළුවා ෆ්‍රික්සස් සහ හෙලේ නම් දරුවන් දෙදෙනා බේරා ගැනීම සඳහා භාවිතා වූ අතර, පසුව එය අහසේ තරු මණ්ඩලයක් ලෙස ස්ථාන ගත කරන ලදී.</p>
                </div>
                
                <div class="myth-card">
                    <div class="myth-icon">🔥</div>
                    <h3>ගින්න මූලද්‍රව්‍යයේ ශක්තිය</h3>
                    <p>මේෂ ලග්නය ගින්න මූලද්‍රව්‍යයට අයත් වන අතර, මෙය ශක්තිය, ධෛර්යය, සහ නායකත්වය නියෝජනය කරයි. ගින්නේ ස්වභාවය මේෂ ලග්නයේ පුද්ගලයන්ගේ ක්‍රියාශීලිත්වය සහ අභිලාෂකාමිත්වය පිළිබිඹු කරයි.</p>
                </div>
                
                <div class="myth-card">
                    <div class="myth-icon">♂</div>
                    <h3>අඟහරු ග්‍රහයාගේ බලපෑම</h3>
                    <p>අඟහරු ග්‍රහයා මේෂ ලග්නයේ පාලක ග්‍රහයා වන අතර, මෙය යුද්ධයේ දෙවියා වන මාර්ස්ගේ නමින් නම් කර ඇත. මෙම ග්‍රහයා ධෛර්යය, ශක්තිය, සහ තරඟකාමිත්වය ලබා දෙයි.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Rituals Section -->
    <section class="rituals">
        <div class="container">
            <h2 class="section-title">චාරිත්‍ර වාරිත්‍ර සහ උපදෙස්</h2>
            
            <div class="rituals-grid">
                <div class="ritual-card">
                    <div class="ritual-icon">🕯️</div>
                    <h3>දෛනික පූජාව</h3>
                    <div class="ritual-content">
                        <p><strong>කාලය:</strong> උදෑසන 6:00 - 7:00</p>
                        <p><strong>දිශාව:</strong> නැගෙනහිර</p>
                        <p><strong>මන්ත්‍රය:</strong> "ඕම් අං අංගාරකාය නමහ"</p>
                        <p><strong>පූජා ද්‍රව්‍ය:</strong> රතු මල්, රතු සන්දල්, රතු වස්ත්‍ර</p>
                        <p>අඟහරු ග්‍රහයාට පූජා කිරීමෙන් මේෂ ලග්නයේ ශක්තිය වර්ධනය වේ.</p>
                    </div>
                </div>
                
                <div class="ritual-card">
                    <div class="ritual-icon">💎</div>
                    <h3>ශුභ මැණික්</h3>
                    <div class="ritual-content">
                        <p><strong>ප්‍රධාන මැණික:</strong> රතු කොරල් (Red Coral)</p>
                        <p><strong>විකල්ප මැණික්:</strong> රුබි, ගාර්නට්</p>
                        <p><strong>ධාරණය කරන ආකාරය:</strong> දකුණු අතේ මුද්‍රිකාවක්</p>
                        <p><strong>ලෝහය:</strong> තඹ හෝ රන්</p>
                        <p>මෙම මැණික් ධාරණය කිරීමෙන් ධෛර්යය සහ ශක්තිය වර්ධනය වේ.</p>
                    </div>
                </div>
                
                <div class="ritual-card">
                    <div class="ritual-icon">🌿</div>
                    <h3>ශුභ ශාක</h3>
                    <div class="ritual-content">
                        <p><strong>ප්‍රධාන ශාකය:</strong> මරිච්චි (Red Chili)</p>
                        <p><strong>අනෙකුත් ශාක:</strong> ගම්මිරිස්, ඉඟුරු, ලූනු</p>
                        <p><strong>භාවිතය:</strong> නිවසේ නැගෙනහිර දිශාවේ වගා කරන්න</p>
                        <p>මෙම ශාක මේෂ ලග්නයේ ගින්න මූලද්‍රව්‍යයට ගැලපේ.</p>
                    </div>
                </div>
                
                <div class="ritual-card">
                    <div class="ritual-icon">🎯</div>
                    <h3>ශක්ති වර්ධන ක්‍රම</h3>
                    <div class="ritual-content">
                        <p><strong>ව්‍යායාම:</strong> උදෑසන ධාවනය හෝ ශක්ති ව්‍යායාම</p>
                        <p><strong>ධ්‍යානය:</strong> ගින්න මූලද්‍රව්‍ය මත අවධානය යොමු කිරීම</p>
                        <p><strong>ආහාර:</strong> කුළුබඩු සහිත ආහාර</p>
                        <p><strong>වර්ණ චිකිත්සාව:</strong> රතු වර්ණයෙන් වට වීම</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Compatibility Section -->
    <section class="compatibility">
        <div class="container">
            <h2 class="section-title">අනෙකුත් ලග්න සමඟ ගැළපීම</h2>
            
            <div class="compatibility-grid">
                <div class="compatibility-card high">
                    <h3>ඉහළ ගැළපීම</h3>
                    <div class="zodiac-matches">
                        <div class="match-item">
                            <span class="zodiac-symbol">♌</span>
                            <span class="zodiac-name">සිංහ</span>
                            <span class="match-percentage">95%</span>
                        </div>
                        <div class="match-item">
                            <span class="zodiac-symbol">♐</span>
                            <span class="zodiac-name">ධනු</span>
                            <span class="match-percentage">90%</span>
                        </div>
                        <div class="match-item">
                            <span class="zodiac-symbol">♊</span>
                            <span class="zodiac-name">මිථුන</span>
                            <span class="match-percentage">85%</span>
                        </div>
                    </div>
                </div>
                
                <div class="compatibility-card medium">
                    <h3>මධ්‍යම ගැළපීම</h3>
                    <div class="zodiac-matches">
                        <div class="match-item">
                            <span class="zodiac-symbol">♒</span>
                            <span class="zodiac-name">කුම්භ</span>
                            <span class="match-percentage">70%</span>
                        </div>
                        <div class="match-item">
                            <span class="zodiac-symbol">♎</span>
                            <span class="zodiac-name">තුලා</span>
                            <span class="match-percentage">65%</span>
                        </div>
                        <div class="match-item">
                            <span class="zodiac-symbol">♈</span>
                            <span class="zodiac-name">මේෂ</span>
                            <span class="match-percentage">60%</span>
                        </div>
                    </div>
                </div>
                
                <div class="compatibility-card low">
                    <h3>අඩු ගැළපීම</h3>
                    <div class="zodiac-matches">
                        <div class="match-item">
                            <span class="zodiac-symbol">♋</span>
                            <span class="zodiac-name">කර්කට</span>
                            <span class="match-percentage">40%</span>
                        </div>
                        <div class="match-item">
                            <span class="zodiac-symbol">♑</span>
                            <span class="zodiac-name">මකර</span>
                            <span class="match-percentage">35%</span>
                        </div>
                        <div class="match-item">
                            <span class="zodiac-symbol">♍</span>
                            <span class="zodiac-name">කන්‍යා</span>
                            <span class="match-percentage">30%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>කුබේර මන්ත්‍රය</h3>
                    <p>ශ්‍රී ලාංකික පුරාණ සම්ප්‍රදායේ අභිරහස් ලෝකය ගවේෂණය කරන්න</p>
                </div>
                <div class="footer-section">
                    <h4>ඉක්මන් සබැඳි</h4>
                    <ul>
                        <li><a href="../index.html">මුල් පිටුව</a></li>
                        <li><a href="../index.html#zodiac">ලග්න</a></li>
                        <li><a href="../store.html">වෙළඳසැල</a></li>
                        <li><a href="../index.html#contact">සම්බන්ධතා</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>සමාජ මාධ්‍ය</h4>
                    <div class="social-links">
                        <a href="#">Facebook</a>
                        <a href="#">Instagram</a>
                        <a href="#">YouTube</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 කුබේර මන්ත්‍රය. සියලුම හිමිකම් ඇවිරිණි.</p>
            </div>
        </div>
    </footer>

    <!-- Load scripts in correct order -->
    <script src="../scripts/env-config.js"></script>
    <script src="../scripts/api-client.js"></script>
    <script src="../scripts/main.js"></script>
    <script src="../scripts/zodiac.js"></script>
    
    <!-- Test API Button -->
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <button id="testApiBtn" class="btn btn-primary">Test API Key</button>
                <div id="apiTestResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>කුබේර මන්ත්‍රය</h3>
                    <p>ශ්‍රී ලාංකික පුරාණ සම්ප්‍රදායේ අභිරහස් ලෝකය ගවේෂණය කරන්න</p>
                </div>
                <div class="footer-section">
                    <h4>ඉක්මන් සබැඳි</h4>
                    <ul>
                        <li><a href="../index.html">මුල් පිටුව</a></li>
                        <li><a href="../index.html#zodiac">ලග්න</a></li>
                        <li><a href="../store.html">වෙළඳසැල</a></li>
                        <li><a href="../index.html#contact">සම්බන්ධතා</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>සමාජ මාධ්‍ය</h4>
                    <div class="social-links">
                        <a href="#">Facebook</a>
                        <a href="#">Instagram</a>
                        <a href="#">YouTube</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 කුබේර මන්ත්‍රය. සියලුම හිමිකම් ඇවිරිණි.</p>
            </div>
        </div>
    </footer>


    
    <!-- Test API Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Test API Key button
            const testApiBtn = document.getElementById('testApiBtn');
            const apiTestResult = document.getElementById('apiTestResult');
            
            if (testApiBtn) {
                testApiBtn.addEventListener('click', async function() {
                    apiTestResult.innerHTML = 'Testing API key...';
                    
                    try {
                        // Get API key from window.ENV or localStorage
                        const apiKey = window.ENV?.OPENAI_API_KEY || localStorage.getItem('OPENAI_API_KEY');
                        
                        // Log API key for debugging
                        console.log('API Key for test (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'No API key');
                        
                        // Make a test request to the server
                        const response = await fetch('/api/openai', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                apiKey: apiKey,
                                requestBody: {
                                    model: 'gpt-3.5-turbo',
                                    messages: [{
                                        role: 'user',
                                        content: 'Say hello in Sinhala'
                                    }],
                                    temperature: 0.7,
                                    max_tokens: 50,
                                    top_p: 0.95
                                }
                            })
                        });
                        
                        const data = await response.json();
                        
                        if (response.ok) {
                            apiTestResult.innerHTML = `<div class="alert alert-success">API test successful! Response: ${data.choices?.[0]?.message?.content || JSON.stringify(data)}</div>`;
                        } else {
                            apiTestResult.innerHTML = `<div class="alert alert-danger">API test failed: ${data.error || 'Unknown error'}</div>`;
                        }
                    } catch (error) {
                        console.error('API test error:', error);
                        apiTestResult.innerHTML = `<div class="alert alert-danger">API test error: ${error.message}</div>`;
                    }
                });
            }
        });
    </script>
</body>
</html>

<div class="container">
    <div class="row">
        <div class="col-12 text-center mb-4">
            <button id="testApiBtn" class="btn btn-primary">Test API Key</button>
            <div id="apiTestResult" class="mt-3"></div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Test API Key button
        const testApiBtn = document.getElementById('testApiBtn');
        const apiTestResult = document.getElementById('apiTestResult');
        
        if (testApiBtn) {
            testApiBtn.addEventListener('click', async function() {
                apiTestResult.innerHTML = 'Testing API key...';
                
                try {
                    // Get API key from window.ENV or localStorage
                    const apiKey = window.ENV?.OPENAI_API_KEY || localStorage.getItem('OPENAI_API_KEY');
                    
                    // Log API key for debugging
                    console.log('API Key for test (first 10 chars):', apiKey ? apiKey.substring(0, 10) + '...' : 'No API key');
                    
                    // Make a test request to the server
                    const response = await fetch('/api/openai', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            apiKey: apiKey,
                            requestBody: {
                                model: 'gpt-3.5-turbo',
                                messages: [{
                                    role: 'user',
                                    content: 'Say hello in Sinhala'
                                }],
                                temperature: 0.7,
                                max_tokens: 50,
                                top_p: 0.95
                            }
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        apiTestResult.innerHTML = `<div class="alert alert-success">API test successful! Response: ${data.choices?.[0]?.message?.content || JSON.stringify(data)}</div>`;
                    } else {
                        apiTestResult.innerHTML = `<div class="alert alert-danger">API test failed: ${data.error || 'Unknown error'}</div>`;
                    }
                } catch (error) {
                    console.error('API test error:', error);
                    apiTestResult.innerHTML = `<div class="alert alert-danger">API test error: ${error.message}</div>`;
                }
            });
        }
    });
</script>