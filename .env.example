# Kubera Manthra Environment Configuration
# Copy this file to .env and configure your settings

# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyBdazvi0etIk23H6Fn7nNpU1BS55H9NNVc

# Server Configuration
PORT=3001
NODE_ENV=development

# Security Configuration (for production)
# CORS_ORIGIN=https://your-domain.com

# Cache Configuration
CACHE_TTL=86400000
# Cache TTL in milliseconds (default: 24 hours)

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=20
RATE_LIMIT_DELAY_BETWEEN_REQUESTS=3000

# API Configuration
API_TIMEOUT=30000
API_MAX_RETRIES=3

# Logging Configuration
LOG_LEVEL=info
# Options: error, warn, info, debug
