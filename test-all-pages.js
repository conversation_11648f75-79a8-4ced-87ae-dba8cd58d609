/**
 * Comprehensive Test Script for Kubera Manthra Application
 * Tests all zodiac pages and API endpoints for consistency and functionality
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
const ZODIAC_SIGNS = [
    'mesha', 'vrishabha', 'mithuna', 'karkata', 
    'simha', 'kanya', 'tula', 'vrischika', 
    'dhanu', 'makara', 'kumbha', 'meena'
];

class ApplicationTester {
    constructor() {
        this.results = {
            apiTests: {},
            pageTests: {},
            scriptTests: {},
            errors: [],
            summary: {}
        };
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting comprehensive application tests...\n');

        try {
            await this.testApiEndpoints();
            await this.testZodiacPages();
            await this.testScriptConsistency();
            this.generateReport();
        } catch (error) {
            console.error('❌ Test execution failed:', error.message);
            this.results.errors.push(`Test execution failed: ${error.message}`);
        }
    }

    /**
     * Test all API endpoints
     */
    async testApiEndpoints() {
        console.log('🔍 Testing API endpoints...');

        // Test health endpoint
        try {
            const response = await fetch(`${BASE_URL}/api/health`);
            const data = await response.json();
            this.results.apiTests.health = {
                status: response.ok ? 'PASS' : 'FAIL',
                responseTime: response.headers.get('x-response-time') || 'N/A',
                geminiAvailable: data.service?.available || false,
                data
            };
            console.log(`  ✅ Health endpoint: ${response.ok ? 'PASS' : 'FAIL'}`);
        } catch (error) {
            this.results.apiTests.health = { status: 'ERROR', error: error.message };
            console.log(`  ❌ Health endpoint: ERROR - ${error.message}`);
        }

        // Test zodiac signs endpoint
        try {
            const response = await fetch(`${BASE_URL}/api/zodiac-signs`);
            const data = await response.json();
            this.results.apiTests.zodiacSigns = {
                status: response.ok ? 'PASS' : 'FAIL',
                signsCount: data.signs?.length || 0,
                data
            };
            console.log(`  ✅ Zodiac signs endpoint: ${response.ok ? 'PASS' : 'FAIL'} (${data.signs?.length || 0} signs)`);
        } catch (error) {
            this.results.apiTests.zodiacSigns = { status: 'ERROR', error: error.message };
            console.log(`  ❌ Zodiac signs endpoint: ERROR - ${error.message}`);
        }

        // Test each zodiac horoscope endpoint
        for (const sign of ZODIAC_SIGNS) {
            try {
                const startTime = Date.now();
                const response = await fetch(`${BASE_URL}/api/daily-horoscope/${sign}`);
                const responseTime = Date.now() - startTime;
                const data = await response.json();
                
                this.results.apiTests[`horoscope_${sign}`] = {
                    status: response.ok ? 'PASS' : 'FAIL',
                    responseTime: `${responseTime}ms`,
                    hasContent: !!(data.content && Object.keys(data.content).length > 0),
                    serviceAvailable: data.metadata?.serviceAvailable || false,
                    cached: data.metadata?.cached || false
                };
                console.log(`  ✅ ${sign} horoscope: ${response.ok ? 'PASS' : 'FAIL'} (${responseTime}ms)`);
            } catch (error) {
                this.results.apiTests[`horoscope_${sign}`] = { status: 'ERROR', error: error.message };
                console.log(`  ❌ ${sign} horoscope: ERROR - ${error.message}`);
            }
        }
    }

    /**
     * Test zodiac page files
     */
    async testZodiacPages() {
        console.log('\n📄 Testing zodiac page files...');

        for (const sign of ZODIAC_SIGNS) {
            const filePath = path.join(__dirname, 'pages', `${sign}.html`);
            
            try {
                if (fs.existsSync(filePath)) {
                    const content = fs.readFileSync(filePath, 'utf8');
                    
                    this.results.pageTests[sign] = {
                        exists: true,
                        hasCorrectScripts: this.checkScriptIncludes(content),
                        hasInitialization: content.includes(`initializeZodiacPage('${sign}')`),
                        hasCorrectTitle: content.includes(sign) || content.includes('ලග්නය'),
                        fileSize: `${Math.round(content.length / 1024)}KB`
                    };
                    
                    const status = this.results.pageTests[sign].hasCorrectScripts && 
                                 this.results.pageTests[sign].hasInitialization ? 'PASS' : 'FAIL';
                    console.log(`  ✅ ${sign}.html: ${status}`);
                } else {
                    this.results.pageTests[sign] = { exists: false };
                    console.log(`  ❌ ${sign}.html: FILE NOT FOUND`);
                }
            } catch (error) {
                this.results.pageTests[sign] = { exists: false, error: error.message };
                console.log(`  ❌ ${sign}.html: ERROR - ${error.message}`);
            }
        }
    }

    /**
     * Check script includes in HTML content
     */
    checkScriptIncludes(content) {
        const requiredScripts = [
            'env-config.js',
            'api-client.js',
            'main.js',
            'zodiac.js'
        ];

        return requiredScripts.every(script => content.includes(script));
    }

    /**
     * Test script file consistency
     */
    async testScriptConsistency() {
        console.log('\n📜 Testing script file consistency...');

        const scriptFiles = [
            'scripts/env-config.js',
            'scripts/api-client.js',
            'scripts/main.js',
            'scripts/zodiac.js',
            'scripts/config.js',
            'scripts/env-loader.js'
        ];

        for (const scriptPath of scriptFiles) {
            const fullPath = path.join(__dirname, scriptPath);
            
            try {
                if (fs.existsSync(fullPath)) {
                    const content = fs.readFileSync(fullPath, 'utf8');
                    this.results.scriptTests[scriptPath] = {
                        exists: true,
                        fileSize: `${Math.round(content.length / 1024)}KB`,
                        hasErrors: this.checkForCommonErrors(content)
                    };
                    console.log(`  ✅ ${scriptPath}: EXISTS (${this.results.scriptTests[scriptPath].fileSize})`);
                } else {
                    this.results.scriptTests[scriptPath] = { exists: false };
                    console.log(`  ❌ ${scriptPath}: NOT FOUND`);
                }
            } catch (error) {
                this.results.scriptTests[scriptPath] = { exists: false, error: error.message };
                console.log(`  ❌ ${scriptPath}: ERROR - ${error.message}`);
            }
        }
    }

    /**
     * Check for common JavaScript errors
     */
    checkForCommonErrors(content) {
        const errors = [];
        
        // Check for OpenAI references that should be removed
        if (content.includes('openai') || content.includes('OPENAI')) {
            errors.push('Contains OpenAI references');
        }
        
        // Check for syntax issues
        if (content.includes('undefined') && content.includes('function')) {
            errors.push('Potential undefined function calls');
        }
        
        return errors;
    }

    /**
     * Generate comprehensive test report
     */
    generateReport() {
        console.log('\n📊 COMPREHENSIVE TEST REPORT');
        console.log('=' .repeat(50));

        // API Tests Summary
        const apiPassed = Object.values(this.results.apiTests).filter(test => test.status === 'PASS').length;
        const apiTotal = Object.keys(this.results.apiTests).length;
        console.log(`\n🔌 API Tests: ${apiPassed}/${apiTotal} PASSED`);

        // Page Tests Summary
        const pagePassed = Object.values(this.results.pageTests).filter(test => 
            test.exists && test.hasCorrectScripts && test.hasInitialization).length;
        const pageTotal = ZODIAC_SIGNS.length;
        console.log(`📄 Page Tests: ${pagePassed}/${pageTotal} PASSED`);

        // Script Tests Summary
        const scriptPassed = Object.values(this.results.scriptTests).filter(test => test.exists).length;
        const scriptTotal = Object.keys(this.results.scriptTests).length;
        console.log(`📜 Script Tests: ${scriptPassed}/${scriptTotal} PASSED`);

        // Overall Status
        const overallPassed = apiPassed + pagePassed + scriptPassed;
        const overallTotal = apiTotal + pageTotal + scriptTotal;
        const successRate = Math.round((overallPassed / overallTotal) * 100);

        console.log(`\n🎯 OVERALL SUCCESS RATE: ${successRate}% (${overallPassed}/${overallTotal})`);

        if (successRate >= 90) {
            console.log('🎉 EXCELLENT! Application is in great shape.');
        } else if (successRate >= 75) {
            console.log('✅ GOOD! Minor issues to address.');
        } else {
            console.log('⚠️  NEEDS ATTENTION! Several issues found.');
        }

        // Save detailed report
        this.saveDetailedReport();
    }

    /**
     * Save detailed test report to file
     */
    saveDetailedReport() {
        const reportPath = path.join(__dirname, 'test-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
        console.log(`\n📋 Detailed report saved to: ${reportPath}`);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new ApplicationTester();
    tester.runAllTests().catch(console.error);
}

module.exports = ApplicationTester;
