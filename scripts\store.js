// Store JavaScript for Kubera Manthra E-commerce

// Product Data
const products = [
    {
        id: 1,
        title: "නව ග්‍රහ මුද්‍රිකාව",
        category: "jewelry",
        price: 12500,
        originalPrice: 15000,
        image: "💍",
        description: "නව ග්‍රහයන්ගේ ශක්තිය ආකර්ෂණය කරන විශේෂ මුද්‍රිකාව. රිදී සහ රත්‍රන් මිශ්‍රණයකින් නිර්මාණය කර ඇත.",
        features: [
            "925 රිදී ගුණත්වය",
            "ස්වභාවික මැණික් ගල්",
            "අතින් කැටයම් කළ සංකේත",
            "ආශීර්වාද සහිත",
            "ජීවිත කාල වගකීම"
        ],
        badge: "sale",
        inStock: true
    },
    {
        id: 2,
        title: "ජ්‍යොතිෂ ශාස්ත්‍ර මාර්ගෝපදේශය",
        category: "books",
        price: 3500,
        originalPrice: null,
        image: "📖",
        description: "සම්පූර්ණ ජ්‍යොතිෂ ශාස්ත්‍රය සිංහලෙන් ලියවුණු විශේෂ ග්‍රන්ථයක්. ආරම්භකයන්ගේ සිට ප්‍රවීණයන් දක්වා සියලු දෙනාට ගැලපේ.",
        features: [
            "500+ පිටු",
            "සිංහල භාෂාව",
            "රූප සහිත විස්තර",
            "ප්‍රායෝගික උදාහරණ",
            "නොමිලේ ඔන්ලයින් සහාය"
        ],
        badge: "new",
        inStock: true
    },
    {
        id: 3,
        title: "නීලා මැණික් ලාංඡනය",
        category: "crystals",
        price: 25000,
        originalPrice: 30000,
        image: "💎",
        description: "ශ්‍රී ලාංකික නීලා මැණිකෙන් නිර්මාණය කළ විශේෂ ලාංඡනය. ප්‍රඥාව සහ ආධ්‍යාත්මික ශක්තිය වර්ධනය කරයි.",
        features: [
            "ස්වභාවික ශ්‍රී ලාංකික නීලා",
            "සහතික සහිත",
            "ශක්ති සම්පාදන චාරිත්‍රය",
            "විශේෂ ඇසුරුම්",
            "රැකවරණ වගකීම"
        ],
        badge: "premium",
        inStock: true
    },
    {
        id: 4,
        title: "පූජා සම්පූර්ණ කට්ටලය",
        category: "ritual",
        price: 8500,
        originalPrice: null,
        image: "🕯️",
        description: "දෛනික පූජා සඳහා අවශ්‍ය සියලුම උපකරණ එක් කට්ටලයක. ගුණත්වයෙන් යුත් සහ ආශීර්වාද ලත්.",
        features: [
            "පිත්තල දීප පහන",
            "සුවඳ දුම්කොළ",
            "පූජා තැටිය",
            "මල් කෝප්ප",
            "මාර්ගෝපදේශ පොත"
        ],
        badge: "new",
        inStock: true
    },
    {
        id: 5,
        title: "රත්නා මාලාව",
        category: "jewelry",
        price: 18000,
        originalPrice: 22000,
        image: "📿",
        description: "විවිධ රත්නා වලින් සාදන ලද විශේෂ මාලාව. මානසික සාමය සහ ධනාත්මක ශක්තිය ලබා දෙයි.",
        features: [
            "108 මණි ගණන",
            "ස්වභාවික රත්නා",
            "අතින් නිර්මාණය",
            "ශක්ති සම්පාදනය",
            "සුන්දර ඇසුරුම්"
        ],
        badge: "sale",
        inStock: true
    },
    {
        id: 6,
        title: "ග්‍රහ ශාන්ති මන්ත්‍ර සංග්‍රහය",
        category: "books",
        price: 2800,
        originalPrice: null,
        image: "📚",
        description: "ග්‍රහ දෝෂ නිවාරණය සඳහා විශේෂ මන්ත්‍ර සහ ක්‍රම ක්‍රමවේද. ශ්‍රී ලාංකික සම්ප්‍රදායට අනුව.",
        features: [
            "සියලුම ග්‍රහ සඳහා මන්ත්‍ර",
            "උච්චාරණ මාර්ගෝපදේශන",
            "කාල නිර්ණය",
            "ප්‍රායෝගික උපදෙස්",
            "ඔඩියෝ සහාය"
        ],
        badge: null,
        inStock: true
    },
    {
        id: 7,
        title: "ස්ඵටික ගෝලය",
        category: "crystals",
        price: 15000,
        originalPrice: null,
        image: "🔮",
        description: "ශුද්ධ ස්ඵටිකයෙන් නිර්මාණය කළ ගෝලාකාර වස්තුව. ධ්‍යානය සහ අනාගත දර්ශනය සඳහා.",
        features: [
            "100% ස්වභාවික ස්ඵටික",
            "අතින් පිරිසිදු කළ",
            "ශක්ති සම්පාදනය",
            "ධ්‍යාන මාර්ගෝපදේශය",
            "ආරක්ෂිත ඇසුරුම්"
        ],
        badge: "premium",
        inStock: true
    },
    {
        id: 8,
        title: "සුරක්ෂිත යන්ත්‍රය",
        category: "ritual",
        price: 6500,
        originalPrice: 8000,
        image: "🛡️",
        description: "නිවස සහ කාර්යාලය සුරක්ෂිත කිරීම සඳහා විශේෂ යන්ත්‍රයක්. පිරිසිදු තඹෙන් නිර්මාණය.",
        features: [
            "තඹ ලෝහයෙන් නිර්මාණය",
            "ශුද්ධ කළ සංකේත",
            "ස්ථාපන මාර්ගෝපදේශන",
            "ආශීර්වාද සහිත",
            "ජීවිත කාල කාර්යක්ෂමතාව"
        ],
        badge: "sale",
        inStock: false
    }
];

// Shopping Cart
let cart = JSON.parse(localStorage.getItem('kuberaCart')) || [];

// DOM Elements
const productsGrid = document.getElementById('productsGrid');
const cartSidebar = document.getElementById('cartSidebar');
const cartOverlay = document.getElementById('cartOverlay');
const cartItems = document.getElementById('cartItems');
const cartCount = document.getElementById('cartCount');
const cartTotal = document.getElementById('cartTotal');
const categoryFilter = document.getElementById('categoryFilter');
const priceFilter = document.getElementById('priceFilter');
const productModal = document.getElementById('productModal');
const modalBody = document.getElementById('modalBody');

// Initialize Store
document.addEventListener('DOMContentLoaded', function() {
    initializeStore();
    initializeFilters();
    initializeCategories();
    updateCartUI();
});

function initializeStore() {
    renderProducts(products);
    updateCartCount();
}

function initializeFilters() {
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterProducts);
    }
    if (priceFilter) {
        priceFilter.addEventListener('change', filterProducts);
    }
}

function initializeCategories() {
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('click', () => {
            const category = card.getAttribute('data-category');
            
            // Update active state
            categoryCards.forEach(c => c.classList.remove('active'));
            card.classList.add('active');
            
            // Filter products
            if (categoryFilter) {
                categoryFilter.value = category;
                filterProducts();
            }
            
            // Scroll to products
            document.querySelector('.products').scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
}

function renderProducts(productsToRender) {
    if (!productsGrid) return;
    
    productsGrid.innerHTML = '';
    
    if (productsToRender.length === 0) {
        productsGrid.innerHTML = `
            <div class="no-products">
                <div class="no-products-icon">🔍</div>
                <h3>නිෂ්පාදන හමු නොවීය</h3>
                <p>කරුණාකර වෙනත් ෆිල්ටරයක් උත්සාහ කරන්න</p>
            </div>
        `;
        return;
    }
    
    productsToRender.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Add animation
    const cards = productsGrid.querySelectorAll('.product-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.innerHTML = `
        <div class="product-image">
            ${product.image}
            ${product.badge ? `<div class="product-badge ${product.badge}">${getBadgeText(product.badge)}</div>` : ''}
        </div>
        <div class="product-info">
            <div class="product-category">${getCategoryName(product.category)}</div>
            <h3 class="product-title">${product.title}</h3>
            <p class="product-description">${product.description}</p>
            <div class="product-price">
                <span class="current-price">රු. ${product.price.toLocaleString()}</span>
                ${product.originalPrice ? `
                    <span class="original-price">රු. ${product.originalPrice.toLocaleString()}</span>
                    <span class="discount">${Math.round((1 - product.price / product.originalPrice) * 100)}% අඩු</span>
                ` : ''}
            </div>
            <div class="product-actions">
                <button class="btn-add-cart" onclick="addToCart(${product.id})" ${!product.inStock ? 'disabled' : ''}>
                    ${product.inStock ? 'කරත්තයට එක් කරන්න' : 'තොගයේ නැත'}
                </button>
                <button class="btn-view-details" onclick="viewProductDetails(${product.id})">
                    විස්තර
                </button>
            </div>
        </div>
    `;
    return card;
}

function getBadgeText(badge) {
    const badges = {
        'new': 'නව',
        'sale': 'අඩු මිල',
        'premium': 'ප්‍රිමියම්'
    };
    return badges[badge] || badge;
}

function getCategoryName(category) {
    const categories = {
        'jewelry': 'ආභරණ',
        'books': 'පොත්',
        'crystals': 'මැණික්',
        'ritual': 'චාරිත්‍ර උපකරණ'
    };
    return categories[category] || category;
}

function filterProducts() {
    const categoryValue = categoryFilter ? categoryFilter.value : 'all';
    const priceValue = priceFilter ? priceFilter.value : 'all';
    
    let filteredProducts = products;
    
    // Filter by category
    if (categoryValue !== 'all') {
        filteredProducts = filteredProducts.filter(product => product.category === categoryValue);
    }
    
    // Filter by price
    if (priceValue !== 'all') {
        switch (priceValue) {
            case 'low':
                filteredProducts = filteredProducts.filter(product => product.price < 5000);
                break;
            case 'medium':
                filteredProducts = filteredProducts.filter(product => product.price >= 5000 && product.price <= 15000);
                break;
            case 'high':
                filteredProducts = filteredProducts.filter(product => product.price > 15000);
                break;
        }
    }
    
    renderProducts(filteredProducts);
}

function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product || !product.inStock) return;
    
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: product.id,
            title: product.title,
            price: product.price,
            image: product.image,
            quantity: 1
        });
    }
    
    saveCart();
    updateCartUI();
    showNotification(`${product.title} කරත්තයට එක් කරන ලදී`, 'success');
    
    // Add visual feedback
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'එක් කරන ලදී ✓';
    button.style.background = '#4caf50';
    
    setTimeout(() => {
        button.textContent = originalText;
        button.style.background = '';
    }, 1500);
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveCart();
    updateCartUI();
    showNotification('නිෂ්පාදනය කරත්තයෙන් ඉවත් කරන ලදී', 'info');
}

function updateQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;
    
    item.quantity += change;
    
    if (item.quantity <= 0) {
        removeFromCart(productId);
    } else {
        saveCart();
        updateCartUI();
    }
}

function updateCartUI() {
    updateCartCount();
    renderCartItems();
    updateCartTotal();
}

function updateCartCount() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    if (cartCount) {
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
    }
}

function renderCartItems() {
    if (!cartItems) return;
    
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <div class="empty-cart-icon">🛒</div>
                <h3>ඔබේ කරත්තය හිස්ය</h3>
                <p>නිෂ්පාදන එක් කිරීමට පටන් ගන්න</p>
            </div>
        `;
        return;
    }
    
    cartItems.innerHTML = cart.map(item => `
        <div class="cart-item">
            <div class="cart-item-image">${item.image}</div>
            <div class="cart-item-info">
                <div class="cart-item-title">${item.title}</div>
                <div class="cart-item-price">රු. ${item.price.toLocaleString()}</div>
            </div>
            <div class="cart-item-controls">
                <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                <span class="quantity-display">${item.quantity}</span>
                <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                <button class="remove-item" onclick="removeFromCart(${item.id})">&times;</button>
            </div>
        </div>
    `).join('');
}

function updateCartTotal() {
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    if (cartTotal) {
        cartTotal.textContent = total.toLocaleString();
    }
}

function toggleCart() {
    const isOpen = cartSidebar.classList.contains('open');
    
    if (isOpen) {
        cartSidebar.classList.remove('open');
        cartOverlay.classList.remove('active');
        document.body.style.overflow = '';
    } else {
        cartSidebar.classList.add('open');
        cartOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function viewProductDetails(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    modalBody.innerHTML = `
        <div class="modal-product-image">${product.image}</div>
        <h2 class="modal-product-title">${product.title}</h2>
        <div class="product-price">
            <span class="current-price">රු. ${product.price.toLocaleString()}</span>
            ${product.originalPrice ? `
                <span class="original-price">රු. ${product.originalPrice.toLocaleString()}</span>
                <span class="discount">${Math.round((1 - product.price / product.originalPrice) * 100)}% අඩු</span>
            ` : ''}
        </div>
        <p class="modal-product-description">${product.description}</p>
        <div class="modal-product-features">
            <h4>විශේෂාංග:</h4>
            <ul>
                ${product.features.map(feature => `<li>${feature}</li>`).join('')}
            </ul>
        </div>
        <div class="modal-actions">
            <div class="quantity-selector">
                <button onclick="changeModalQuantity(-1)">-</button>
                <input type="number" id="modalQuantity" value="1" min="1" max="10">
                <button onclick="changeModalQuantity(1)">+</button>
            </div>
            <button class="btn-add-cart" onclick="addToCartFromModal(${product.id})" ${!product.inStock ? 'disabled' : ''}>
                ${product.inStock ? 'කරත්තයට එක් කරන්න' : 'තොගයේ නැත'}
            </button>
        </div>
    `;
    
    productModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeProductModal() {
    productModal.classList.remove('active');
    document.body.style.overflow = '';
}

function changeModalQuantity(change) {
    const quantityInput = document.getElementById('modalQuantity');
    if (!quantityInput) return;
    
    let newValue = parseInt(quantityInput.value) + change;
    newValue = Math.max(1, Math.min(10, newValue));
    quantityInput.value = newValue;
}

function addToCartFromModal(productId) {
    const product = products.find(p => p.id === productId);
    const quantityInput = document.getElementById('modalQuantity');
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;
    
    if (!product || !product.inStock) return;
    
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            id: product.id,
            title: product.title,
            price: product.price,
            image: product.image,
            quantity: quantity
        });
    }
    
    saveCart();
    updateCartUI();
    closeProductModal();
    showNotification(`${product.title} (${quantity}) කරත්තයට එක් කරන ලදී`, 'success');
}

function checkout() {
    if (cart.length === 0) {
        showNotification('කරත්තය හිස්ය', 'error');
        return;
    }
    
    // Simulate checkout process
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    showNotification('ගනුදෙනුව සකස් කරමින්...', 'info');
    
    setTimeout(() => {
        // Clear cart
        cart = [];
        saveCart();
        updateCartUI();
        toggleCart();
        
        showNotification(`ගනුදෙනුව සාර්ථකයි! මුළු මුදල: රු. ${total.toLocaleString()}`, 'success');
        
        // Redirect to thank you page or show success modal
        setTimeout(() => {
            alert('ස්තූතියි! ඔබේ ඇණවුම සාර්ථකව ලබා ගන්නා ලදී. අප ඉක්මනින් ඔබ සමඟ සම්බන්ධ වෙමු.');
        }, 1000);
    }, 2000);
}

function saveCart() {
    localStorage.setItem('kuberaCart', JSON.stringify(cart));
}

// Close modals when clicking outside
productModal.addEventListener('click', (e) => {
    if (e.target === productModal) {
        closeProductModal();
    }
});

// Keyboard navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        if (productModal.classList.contains('active')) {
            closeProductModal();
        } else if (cartSidebar.classList.contains('open')) {
            toggleCart();
        }
    }
});

// Search functionality (future enhancement)
function searchProducts(query) {
    const filteredProducts = products.filter(product => 
        product.title.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase())
    );
    renderProducts(filteredProducts);
}

// Wishlist functionality (future enhancement)
let wishlist = JSON.parse(localStorage.getItem('kuberaWishlist')) || [];

function toggleWishlist(productId) {
    const index = wishlist.indexOf(productId);
    if (index > -1) {
        wishlist.splice(index, 1);
        showNotification('ප්‍රියතම ලැයිස්තුවෙන් ඉවත් කරන ලදී', 'info');
    } else {
        wishlist.push(productId);
        showNotification('ප්‍රියතම ලැයිස්තුවට එක් කරන ලදී', 'success');
    }
    localStorage.setItem('kuberaWishlist', JSON.stringify(wishlist));
}

// Export functions for global access
window.addToCart = addToCart;
window.removeFromCart = removeFromCart;
window.updateQuantity = updateQuantity;
window.toggleCart = toggleCart;
window.viewProductDetails = viewProductDetails;
window.closeProductModal = closeProductModal;
window.changeModalQuantity = changeModalQuantity;
window.addToCartFromModal = addToCartFromModal;
window.checkout = checkout;
window.toggleWishlist = toggleWishlist;
window.searchProducts = searchProducts;

// Initialize store when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeStore);
} else {
    initializeStore();
}