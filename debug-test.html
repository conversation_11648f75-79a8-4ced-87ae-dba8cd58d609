<!DOCTYPE html>
<html>
<head>
    <title>Debug Test</title>
</head>
<body>
    <h1>API Key Debug Test</h1>
    <div id="output"></div>
    <button id="testApiBtn">Test API Call</button>
    <div id="apiResult"></div>
    
    <script src="scripts/env-config.js"></script>
    <script src="scripts/config.js"></script>
    <script>
        const output = document.getElementById('output');
        const apiResult = document.getElementById('apiResult');
        
        // Test 1: Check if env-config loaded
        output.innerHTML += '<p>1. window.ENV: ' + JSON.stringify(window.ENV) + '</p>';
        
        // Test 2: Check localStorage
        output.innerHTML += '<p>2. localStorage API key: ' + localStorage.getItem('OPENAI_API_KEY') + '</p>';
            
            // Test 3: Check config object
            const config = new Config();
            output.innerHTML += '<p>3. Config API key: ' + config.openaiApiKey + '</p>';
            
            // Test 4: Check if API key is configured
            output.innerHTML += '<p>4. Is API key configured: ' + config.isApiKeyConfigured() + '</p>';
            
            // Test 5: Get OpenAI config
            const openaiConfig = config.getOpenAIConfig();
            output.innerHTML += '<p>5. OpenAI config: ' + JSON.stringify(openaiConfig) + '</p>';
        
        // Test API call
        document.getElementById('testApiBtn').addEventListener('click', async () => {
            apiResult.innerHTML = '<p>Testing API call...</p>';
            
            try {
                const requestBody = {
                    model: 'gpt-3.5-turbo',
                    messages: [{
                        role: 'user',
                        content: 'Hello, please respond with a simple greeting in Sinhala.'
                    }],
                    temperature: 0.7,
                    max_tokens: 100
                };
                
                // Use proxy server instead of direct API call
                const response = await fetch('/api/openai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        apiKey: 'sk-your_openai_api_key_here',
                        requestBody: requestBody
                    })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API request failed: ${response.status} - ${errorText}`);
                }
                
                const data = await response.json();
                
                if (data.choices && data.choices[0] && data.choices[0].message) {
                    apiResult.innerHTML = `<p><strong>Success!</strong><br>Response: ${data.choices[0].message.content}</p>`;
                } else {
                    apiResult.innerHTML = `<p><strong>Error:</strong> Invalid response format<br>Response: ${JSON.stringify(data, null, 2)}</p>`;
                }
                
            } catch (error) {
                apiResult.innerHTML = '<p style="color: red;">API Error: ' + error.message + '</p>';
                console.error('API test error:', error);
            }
        });
    </script>
</body>
</html>