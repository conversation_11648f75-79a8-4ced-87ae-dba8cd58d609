# කුබේර මන්ත්‍රය - Sinhala Mythology Website

A modern, fully responsive Sinhala-language website dedicated to Sri Lankan mythology and astrology, featuring twelve zodiac pages with daily content updates via OpenAI API.

## Features

### 🌟 Landing Page
- Sleek, animated design with smooth transitions
- High-quality mythological imagery
- Interactive zodiac grid
- Responsive navigation
- Contact form with Sinhala validation

### 🔮 Zodiac Pages (12 Pages)
- **<PERSON><PERSON> (මේෂ)** - Aries
- **<PERSON><PERSON><PERSON><PERSON> (වෘෂභ)** - <PERSON><PERSON>
- **<PERSON><PERSON><PERSON> (මිථුන)** - <PERSON> (Zodiac Sign)
- **<PERSON><PERSON><PERSON> (කර්කට)** - Cancer
- **<PERSON><PERSON><PERSON> (සිංහ)** - Leo
- **<PERSON><PERSON> (කන්‍යා)** - Virgo
- **<PERSON><PERSON> (තුලා)** - <PERSON>bra
- **<PERSON><PERSON><PERSON><PERSON> (වෘශ්චික)** - Scorpio
- **<PERSON><PERSON><PERSON> (ධනු)** - Sagittarius
- **<PERSON><PERSON> (මකර)** - Capricorn
- **<PERSON><PERSON><PERSON> (කුම්භ)** - Aquarius
- **<PERSON><PERSON> (මීන)** - Pisces

Each zodiac page includes:
- Daily predictions automatically refreshed via OpenAI API
- Wealth, love, and health forecasts
- Auspicious timings and lucky colors
- Detailed characteristics and personality traits
- Mythology and symbolism
- Rituals and spiritual practices
- Compatibility with other signs

### 🛒 E-commerce Store
- Product catalog with mythological themes
- Shopping cart functionality
- Sinhala product descriptions
- Responsive design
- Local storage for cart persistence

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js with Express.js
- **Styling**: Custom CSS with animations and responsive design
- **Fonts**: Noto Sans Sinhala for proper Sinhala text rendering
- **API Integration**: Secure OpenAI GPT-3.5-turbo API integration
- **Architecture**: Modern MVC pattern with service layer
- **Configuration**: Environment-based configuration with .env files
- **Storage**: Intelligent caching with localStorage and memory cache
- **Security**: CORS protection, input validation, and secure API handling
- **Rate Limiting**: Built-in API rate limiting and retry logic
- **PWA**: Service Worker for offline functionality
- **Error Handling**: Comprehensive error handling with fallback content

## Setup Instructions

### Prerequisites
- Node.js (version 14 or higher)
- npm (Node Package Manager)
- OpenAI API key (for AI-powered daily content)
- Modern web browser with JavaScript enabled

### Installation

1. **Clone or download the project files**
   ```bash
   git clone <repository-url>
   cd Kubera_Manthra
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   npm run setup
   ```
   This creates a `.env` file from `.env.example`. Edit the `.env` file and add your OpenAI API key:
   ```env
   OPENAI_API_KEY=sk-your-actual-api-key-here
   PORT=3001
   NODE_ENV=development
   ```

4. **Get your OpenAI API key**
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create a new API key
   - Copy the key and paste it in your `.env` file

5. **Start the server**

   **Development mode:**
   ```bash
   npm run dev
   ```

   **Production mode:**
   ```bash
   npm run prod
   ```

   The server will start on http://localhost:3001

6. **Verify installation**
   ```bash
   npm run health
   ```
   This checks if the API service is running correctly.

## API Endpoints

The application provides a RESTful API for horoscope data:

### Health Check
- **GET** `/api/health` - Check service status and availability

### Zodiac Signs
- **GET** `/api/zodiac-signs` - Get all zodiac signs with their configurations

### Daily Horoscope
- **GET** `/api/daily-horoscope/:zodiacSign` - Get daily horoscope for a specific zodiac sign
  - Parameters: `zodiacSign` (mesha, vrishabha, mithuna, etc.)
  - Returns: Complete horoscope data with predictions, forecasts, and metadata

### Cache Management
- **POST** `/api/clear-cache` - Clear server-side cache (for maintenance)

### File Structure
```
Kubera_Manthra/
├── index.html              # Landing page
├── store.html              # E-commerce store
├── server.js               # Express.js server
├── package.json            # Node.js dependencies
├── .env.example            # Environment configuration template
├── README.md               # This file
├── controllers/            # API controllers
│   └── horoscope-controller.js
├── services/               # Business logic services
│   └── openai-service.js
├── pages/                  # Zodiac pages
│   ├── mesha.html
│   ├── vrishabha.html
│   ├── mithuna.html
│   ├── karkata.html
│   ├── simha.html
│   ├── kanya.html
│   ├── tula.html
│   ├── vrischika.html
│   ├── dhanu.html
│   ├── makara.html
│   ├── kumbha.html
│   └── meena.html
├── styles/                 # CSS files
│   ├── main.css           # Main styles
│   ├── store.css          # Store styles
│   └── zodiac.css         # Zodiac page styles
├── scripts/                # JavaScript files
│   ├── main.js            # Main functionality
│   ├── api-client.js      # Modern API client
│   ├── env-config.js      # Client environment configuration
│   ├── store.js           # Store functionality
│   ├── zodiac.js          # Zodiac API integration
│   └── sw.js              # Service worker
└── images/                 # Image assets (to be added)
```

## Features in Detail

### Daily Content Updates
- Content automatically refreshes every 24 hours
- Fallback content available when API is unavailable
- Loading states and error handling
- Content cached locally for offline access

### Responsive Design
- Mobile-first approach
- Breakpoints: 768px (tablet), 1024px (desktop)
- Touch-friendly navigation
- Optimized for all screen sizes

### Accessibility
- Semantic HTML structure
- Proper heading hierarchy
- Alt text for images
- Keyboard navigation support
- High contrast ratios

### Performance
- Optimized CSS and JavaScript
- Image lazy loading (when images are added)
- Service worker for caching
- Minimal external dependencies

## Customization

### Adding Images
1. Add image files to the `images/` directory
2. Update the relevant HTML files to reference the images
3. Ensure images are optimized for web (WebP format recommended)

### Modifying Content
- **Static content**: Edit the HTML files directly
- **Daily content**: Modify the prompts in `scripts/zodiac.js`
- **Styling**: Update the CSS files in the `styles/` directory

### API Configuration

🚀 **NEW: Enhanced OpenAI API Integration**

The website now features a completely redesigned API integration with:
- **Latest Model**: GPT-3.5-turbo for faster, more accurate responses
- **Secure Configuration**: Environment-based API key management
- **Rate Limiting**: Built-in protection against API quota exhaustion
- **Enhanced Prompts**: More detailed and contextual Sinhala content
- **Error Handling**: Robust fallback mechanisms

#### Quick Setup
1. **Get API Key**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Configure**: Edit `.env` file and add your API key
3. **Test**: Run `npm start` and visit any zodiac page

For detailed setup instructions, see [SETUP.md](SETUP.md)

### API Features
- ✅ **Real-time Content**: Fresh predictions generated daily
- ✅ **Smart Caching**: Reduces API calls and improves performance
- ✅ **Fallback Content**: Seamless experience even without API
- ✅ **Rate Limiting**: Prevents quota exhaustion
- ✅ **Error Recovery**: Automatic retry with exponential backoff

## Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License
All rights reserved © 2024 කුබේර මන්ත්‍රය

## Support
For technical support or questions about the website, please contact through the website's contact form.

---

**Note**: The website works perfectly without API configuration, showing high-quality fallback content. API integration enhances the experience with personalized, real-time predictions.