# කුබේර මන්ත්‍රය - Sinhala Mythology Website

A modern, fully responsive Sinhala-language website dedicated to Sri Lankan mythology and astrology, featuring twelve zodiac pages with daily content updates via OpenAI API.

## Features

### 🌟 Landing Page
- Sleek, animated design with smooth transitions
- High-quality mythological imagery
- Interactive zodiac grid
- Responsive navigation
- Contact form with Sinhala validation

### 🔮 Zodiac Pages (12 Pages)
- **<PERSON><PERSON> (මේෂ)** - Aries
- **<PERSON><PERSON><PERSON><PERSON> (වෘෂභ)** - <PERSON><PERSON>
- **<PERSON><PERSON><PERSON> (මිථුන)** - <PERSON> (Zodiac Sign)
- **<PERSON><PERSON><PERSON> (කර්කට)** - Cancer
- **<PERSON><PERSON><PERSON> (සිංහ)** - Leo
- **<PERSON><PERSON> (කන්‍යා)** - Virgo
- **<PERSON><PERSON> (තුලා)** - <PERSON>bra
- **<PERSON><PERSON><PERSON><PERSON> (වෘශ්චික)** - Scorpio
- **<PERSON><PERSON><PERSON> (ධනු)** - Sagittarius
- **<PERSON><PERSON> (මකර)** - Capricorn
- **<PERSON><PERSON><PERSON> (කුම්භ)** - Aquarius
- **<PERSON><PERSON> (මීන)** - Pisces

Each zodiac page includes:
- Daily predictions automatically refreshed via OpenAI API
- Wealth, love, and health forecasts
- Auspicious timings and lucky colors
- Detailed characteristics and personality traits
- Mythology and symbolism
- Rituals and spiritual practices
- Compatibility with other signs

### 🛒 E-commerce Store
- Product catalog with mythological themes
- Shopping cart functionality
- Sinhala product descriptions
- Responsive design
- Local storage for cart persistence

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Custom CSS with animations and responsive design
- **Fonts**: Noto Sans Sinhala for proper Sinhala text rendering
- **API Integration**: OpenAI GPT-3.5-turbo API for real-time daily content generation
- **Configuration**: Environment-based API key management
- **Storage**: Local Storage for cart and content caching
- **Rate Limiting**: Built-in API rate limiting and error handling
- **PWA**: Service Worker for offline functionality

## Setup Instructions

### Prerequisites
- Modern web browser with JavaScript enabled
- OpenAI API key (for daily content updates)
- Local web server (optional, for development)

### Installation

1. **Clone or download the project files**
   ```
   All files should be in the Kubera_Manthra directory
   ```

2. **Configure OpenAI API**
   - Get your OpenAI API key from OpenAI Platform
   - Open `scripts/zodiac.js`
   - Replace `'YOUR_OPENAI_API_KEY_HERE'` with your actual API key:
   ```javascript
   const OPENAI_API_KEY = 'sk-your-actual-api-key-here';
   ```

3. **Serve the website**
   
   **Option A: Using Python (if installed)**
   ```bash
   cd Kubera_Manthra
   python -m http.server 8000
   ```
   Then visit: http://localhost:8000
   
   **Option B: Using Node.js (if installed)**
   ```bash
   cd Kubera_Manthra
   npx serve .
   ```
   
   **Option C: Using any local web server**
   - Point your web server to the Kubera_Manthra directory
   - Access via your server's URL
   
   **Option D: Direct file access (limited functionality)**
   - Open `index.html` directly in your browser
   - Note: Some features may not work due to CORS restrictions

### File Structure
```
Kubera_Manthra/
├── index.html              # Landing page
├── store.html              # E-commerce store
├── README.md               # This file
├── pages/                  # Zodiac pages
│   ├── mesha.html
│   ├── vrishabha.html
│   ├── mithuna.html
│   ├── karkata.html
│   ├── simha.html
│   ├── kanya.html
│   ├── tula.html
│   ├── vrischika.html
│   ├── dhanu.html
│   ├── makara.html
│   ├── kumbha.html
│   └── meena.html
├── styles/                 # CSS files
│   ├── main.css           # Main styles
│   ├── store.css          # Store styles
│   └── zodiac.css         # Zodiac page styles
├── scripts/                # JavaScript files
│   ├── main.js            # Main functionality
│   ├── store.js           # Store functionality
│   ├── zodiac.js          # Zodiac API integration
│   └── sw.js              # Service worker
└── images/                 # Image assets (to be added)
```

## Features in Detail

### Daily Content Updates
- Content automatically refreshes every 24 hours
- Fallback content available when API is unavailable
- Loading states and error handling
- Content cached locally for offline access

### Responsive Design
- Mobile-first approach
- Breakpoints: 768px (tablet), 1024px (desktop)
- Touch-friendly navigation
- Optimized for all screen sizes

### Accessibility
- Semantic HTML structure
- Proper heading hierarchy
- Alt text for images
- Keyboard navigation support
- High contrast ratios

### Performance
- Optimized CSS and JavaScript
- Image lazy loading (when images are added)
- Service worker for caching
- Minimal external dependencies

## Customization

### Adding Images
1. Add image files to the `images/` directory
2. Update the relevant HTML files to reference the images
3. Ensure images are optimized for web (WebP format recommended)

### Modifying Content
- **Static content**: Edit the HTML files directly
- **Daily content**: Modify the prompts in `scripts/zodiac.js`
- **Styling**: Update the CSS files in the `styles/` directory

### API Configuration

🚀 **NEW: Enhanced OpenAI API Integration**

The website now features a completely redesigned API integration with:
- **Latest Model**: GPT-3.5-turbo for faster, more accurate responses
- **Secure Configuration**: Environment-based API key management
- **Rate Limiting**: Built-in protection against API quota exhaustion
- **Enhanced Prompts**: More detailed and contextual Sinhala content
- **Error Handling**: Robust fallback mechanisms

#### Quick Setup
1. **Get API Key**: Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Configure**: Edit `.env` file and add your API key
3. **Test**: Run `npm start` and visit any zodiac page

For detailed setup instructions, see [SETUP.md](SETUP.md)

### API Features
- ✅ **Real-time Content**: Fresh predictions generated daily
- ✅ **Smart Caching**: Reduces API calls and improves performance
- ✅ **Fallback Content**: Seamless experience even without API
- ✅ **Rate Limiting**: Prevents quota exhaustion
- ✅ **Error Recovery**: Automatic retry with exponential backoff

## Browser Support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License
All rights reserved © 2024 කුබේර මන්ත්‍රය

## Support
For technical support or questions about the website, please contact through the website's contact form.

---

**Note**: The website works perfectly without API configuration, showing high-quality fallback content. API integration enhances the experience with personalized, real-time predictions.