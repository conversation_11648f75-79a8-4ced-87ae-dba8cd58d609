/**
 * API Client for Kubera Manthra
 * Modern client-side API service with caching, error handling, and fallback support
 */

class ApiClient {
    constructor() {
        this.baseUrl = '';
        this.cache = new Map();
        this.cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours
        this.requestTimeout = 15000; // 15 seconds
        this.retryAttempts = 2;
        this.retryDelay = 1000; // 1 second
        
        this.init();
    }

    /**
     * Initialize the API client
     */
    async init() {
        try {
            // Check if API service is available
            const isAvailable = await this.checkServiceHealth();
            if (!isAvailable) {
                console.warn('API service not available, using fallback mode');
                if (typeof showServiceStatus === 'function') {
                    showServiceStatus('fallback', 'API service unavailable. Using static content.');
                }
            }
        } catch (error) {
            console.error('Failed to initialize API client:', error.message);
        }
    }

    /**
     * Check service health
     * @returns {Promise<boolean>}
     */
    async checkServiceHealth() {
        try {
            const response = await this.makeRequest('/api/health', {
                method: 'GET',
                timeout: 5000
            });
            return response.success === true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Make HTTP request with timeout and retry logic
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>}
     */
    async makeRequest(url, options = {}) {
        const {
            method = 'GET',
            body = null,
            headers = {},
            timeout = this.requestTimeout,
            retries = this.retryAttempts
        } = options;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const requestOptions = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            },
            signal: controller.signal
        };

        if (body) {
            requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
        }

        let lastError;

        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                if (attempt > 0) {
                    // Add delay between retries
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
                    console.log(`Retrying request to ${url} (attempt ${attempt + 1})`);
                }

                const response = await fetch(this.baseUrl + url, requestOptions);
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data;

            } catch (error) {
                lastError = error;
                
                if (error.name === 'AbortError') {
                    throw new Error('Request timeout');
                }

                // Don't retry on client errors (4xx)
                if (error.message.includes('HTTP 4')) {
                    throw error;
                }

                console.warn(`Request attempt ${attempt + 1} failed:`, error.message);
            }
        }

        clearTimeout(timeoutId);
        throw lastError;
    }

    /**
     * Get cache key for zodiac data
     * @param {string} zodiacSign - Zodiac sign
     * @returns {string}
     */
    getCacheKey(zodiacSign) {
        const today = new Date().toISOString().split('T')[0];
        return `horoscope_${zodiacSign}_${today}`;
    }

    /**
     * Get cached data
     * @param {string} cacheKey - Cache key
     * @returns {Object|null}
     */
    getCachedData(cacheKey) {
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        
        // Remove expired cache
        if (cached) {
            this.cache.delete(cacheKey);
        }
        
        return null;
    }

    /**
     * Cache data
     * @param {string} cacheKey - Cache key
     * @param {Object} data - Data to cache
     */
    setCachedData(cacheKey, data) {
        this.cache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });

        // Also cache in localStorage for persistence
        try {
            localStorage.setItem(cacheKey, JSON.stringify({
                data,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.warn('Failed to cache data in localStorage:', error.message);
        }
    }

    /**
     * Load cached data from localStorage
     * @param {string} cacheKey - Cache key
     * @returns {Object|null}
     */
    loadFromLocalStorage(cacheKey) {
        try {
            const cached = localStorage.getItem(cacheKey);
            if (cached) {
                const parsed = JSON.parse(cached);
                if (Date.now() - parsed.timestamp < this.cacheTimeout) {
                    return parsed.data;
                } else {
                    localStorage.removeItem(cacheKey);
                }
            }
        } catch (error) {
            console.warn('Failed to load cached data from localStorage:', error.message);
        }
        return null;
    }

    /**
     * Get daily horoscope for zodiac sign
     * @param {string} zodiacSign - Zodiac sign
     * @returns {Promise<Object>}
     */
    async getDailyHoroscope(zodiacSign) {
        const cacheKey = this.getCacheKey(zodiacSign);
        
        // Try memory cache first
        let cachedData = this.getCachedData(cacheKey);
        if (cachedData) {
            console.log(`Using cached horoscope data for ${zodiacSign}`);
            return cachedData;
        }

        // Try localStorage cache
        cachedData = this.loadFromLocalStorage(cacheKey);
        if (cachedData) {
            console.log(`Using localStorage cached horoscope data for ${zodiacSign}`);
            this.setCachedData(cacheKey, cachedData); // Update memory cache
            return cachedData;
        }

        try {
            console.log(`Fetching fresh horoscope data for ${zodiacSign}`);
            const response = await this.makeRequest(`/api/daily-horoscope/${zodiacSign}`);
            
            if (response.success) {
                this.setCachedData(cacheKey, response);
                return response;
            } else {
                throw new Error(response.error || 'Failed to fetch horoscope data');
            }

        } catch (error) {
            console.error(`Failed to fetch horoscope for ${zodiacSign}:`, error.message);
            
            // Try to return fallback content
            return this.getFallbackHoroscope(zodiacSign);
        }
    }

    /**
     * Get all zodiac signs
     * @returns {Promise<Object>}
     */
    async getZodiacSigns() {
        const cacheKey = 'zodiac_signs';
        
        // Try cache first
        const cachedData = this.getCachedData(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        try {
            const response = await this.makeRequest('/api/zodiac-signs');
            
            if (response.success) {
                this.setCachedData(cacheKey, response);
                return response;
            } else {
                throw new Error(response.error || 'Failed to fetch zodiac signs');
            }

        } catch (error) {
            console.error('Failed to fetch zodiac signs:', error.message);
            return this.getFallbackZodiacSigns();
        }
    }

    /**
     * Get fallback horoscope data
     * @param {string} zodiacSign - Zodiac sign
     * @returns {Object}
     */
    getFallbackHoroscope(zodiacSign) {
        // This would contain the same fallback data as in the server
        const fallbackContent = {
            dailyPrediction: `${zodiacSign} ලග්නයේ පුද්ගලයන්ට අද දිනය ධනාත්මක අත්දැකීම් ගෙන එනු ඇත.`,
            wealthForecast: `ධන කටයුතුවල ප්‍රවේශම්කාරී තීරණ ගැනීමෙන් වළකින්න.`,
            loveForecast: `ප්‍රේම සම්බන්ධතාවල අවබෝධය සහ ඉවසීම වැදගත් වේ.`,
            healthForecast: `සමතුලිත ජීවන රටාවක් පවත්වන්න.`,
            auspiciousTime: `උදෑසන 6:00 - 8:00 සහ සවස 6:00 - 8:00 අතර කාල වේලාවන් ශුභ වේ.`,
            luckyColor: `අද දිනය සඳහා නිල් සහ සුදු වර්ණ ශුභ වේ.`
        };

        return {
            success: true,
            zodiacSign,
            content: fallbackContent,
            metadata: {
                generatedAt: new Date().toISOString(),
                serviceAvailable: false,
                cached: false,
                fallback: true
            }
        };
    }

    /**
     * Get fallback zodiac signs data
     * @returns {Object}
     */
    getFallbackZodiacSigns() {
        // Basic zodiac signs data for fallback
        const signs = [
            { key: 'mesha', name: 'මේෂ', symbol: '♈' },
            { key: 'vrishabha', name: 'වෘෂභ', symbol: '♉' },
            { key: 'mithuna', name: 'මිථුන', symbol: '♊' },
            { key: 'karkata', name: 'කර්කට', symbol: '♋' },
            { key: 'simha', name: 'සිංහ', symbol: '♌' },
            { key: 'kanya', name: 'කන්‍යා', symbol: '♍' },
            { key: 'tula', name: 'තුලා', symbol: '♎' },
            { key: 'vrischika', name: 'වෘශ්චික', symbol: '♏' },
            { key: 'dhanu', name: 'ධනු', symbol: '♐' },
            { key: 'makara', name: 'මකර', symbol: '♑' },
            { key: 'kumbha', name: 'කුම්භ', symbol: '♒' },
            { key: 'meena', name: 'මීන', symbol: '♓' }
        ];

        return {
            success: true,
            signs,
            total: signs.length,
            fallback: true
        };
    }

    /**
     * Clear all cached data
     */
    clearCache() {
        this.cache.clear();

        // Clear localStorage cache
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('horoscope_') ||
                    key.startsWith('dailyContent_') ||
                    key.startsWith('lastRefresh_') ||
                    key === 'zodiac_signs' ||
                    key.includes('openai') ||
                    key.includes('OPENAI')) {
                    localStorage.removeItem(key);
                }
            });
        } catch (error) {
            console.warn('Failed to clear localStorage cache:', error.message);
        }

        // Clear any service worker cache if available
        if ('serviceWorker' in navigator && 'caches' in window) {
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName.includes('kubera') || cacheName.includes('horoscope')) {
                            return caches.delete(cacheName);
                        }
                    })
                );
            }).then(() => {
                console.log('Service worker cache cleared');
            }).catch(error => {
                console.warn('Failed to clear service worker cache:', error.message);
            });
        }

        console.log('All caches cleared successfully');
    }

    /**
     * Get cache statistics
     * @returns {Object}
     */
    getCacheStats() {
        return {
            memoryCache: this.cache.size,
            cacheTimeout: this.cacheTimeout,
            requestTimeout: this.requestTimeout,
            retryAttempts: this.retryAttempts
        };
    }
}

// Create global instance
const apiClient = new ApiClient();

// Export for use in other scripts
if (typeof window !== 'undefined') {
    window.ApiClient = ApiClient;
    window.apiClient = apiClient;

    // Global cache clearing function for debugging
    window.clearAllCaches = function() {
        console.log('Clearing all application caches...');
        apiClient.clearCache();

        // Force reload after cache clear
        setTimeout(() => {
            console.log('Cache cleared. Reloading page...');
            window.location.reload(true);
        }, 1000);
    };

    // Global function to check API health
    window.checkApiHealth = async function() {
        try {
            const health = await apiClient.checkServiceHealth();
            console.log('API Health Status:', health ? 'Available' : 'Unavailable');
            return health;
        } catch (error) {
            console.error('API Health Check Failed:', error.message);
            return false;
        }
    };
}
