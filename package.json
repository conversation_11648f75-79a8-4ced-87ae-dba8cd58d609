{"name": "kubera-manthra", "version": "1.0.0", "description": "A modern, fully responsive Sinhala-language website dedicated to Sri Lankan mythology and astrology with real-time OpenAI API integration", "main": "server.js", "scripts": {"start": "node server.js", "serve": "python -m http.server 8000", "dev": "node server.js", "setup": "echo Please read SETUP.md for OpenAI API configuration instructions", "install-deps": "npm install"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.7.0"}, "keywords": ["sinhala", "mythology", "astrology", "zodiac", "sri-lanka"], "author": "Kubera Manthra Team", "license": "MIT", "repository": {"type": "git", "url": "."}, "homepage": "."}