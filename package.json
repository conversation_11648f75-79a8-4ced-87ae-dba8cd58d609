{"name": "kubera-manthra", "version": "2.0.0", "description": "A modern, fully responsive Sinhala-language website dedicated to Sri Lankan mythology and astrology with secure OpenAI API integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "prod": "NODE_ENV=production node server.js", "test": "node --test", "setup": "cp .env.example .env && echo 'Please configure your .env file with your OpenAI API key'", "health": "curl -f http://localhost:3001/api/health || exit 1", "clear-cache": "curl -X POST http://localhost:3001/api/clear-cache", "install-deps": "npm install"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["sinhala", "mythology", "astrology", "zodiac", "sri-lanka", "openai", "api", "horoscope"], "author": "Kubera Manthra Team", "license": "MIT", "repository": {"type": "git", "url": "."}, "homepage": ".", "engines": {"node": ">=14.0.0"}}