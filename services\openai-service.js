/**
 * OpenAI Service for Kubera Manthra
 * Handles all OpenAI API interactions with proper error handling, caching, and rate limiting
 */

const fetch = require('node-fetch');
const fs = require('fs').promises;
const path = require('path');

class OpenAIService {
    constructor() {
        this.apiKey = process.env.OPENAI_API_KEY;
        this.baseUrl = 'https://api.openai.com/v1/chat/completions';
        this.model = 'gpt-3.5-turbo';
        this.cache = new Map();
        this.rateLimiter = {
            requests: [],
            maxRequests: 20,
            windowMs: 60000 // 1 minute
        };
        
        // Initialize service
        this.init();
    }

    /**
     * Initialize the OpenAI service
     */
    async init() {
        try {
            // Validate API key
            if (!this.apiKey) {
                console.warn('OpenAI API key not configured. Service will run in fallback mode.');
                return;
            }

            if (!this.isValidApiKey(this.apiKey)) {
                console.error('Invalid OpenAI API key format.');
                this.apiKey = null;
                return;
            }

            console.log('OpenAI service initialized successfully');
        } catch (error) {
            console.error('Failed to initialize OpenAI service:', error.message);
        }
    }

    /**
     * Validate API key format
     * @param {string} apiKey - The API key to validate
     * @returns {boolean}
     */
    isValidApiKey(apiKey) {
        return typeof apiKey === 'string' && 
               apiKey.startsWith('sk-') && 
               apiKey.length >= 20;
    }

    /**
     * Check if service is available
     * @returns {boolean}
     */
    isAvailable() {
        return !!this.apiKey;
    }

    /**
     * Rate limiting check
     * @returns {Promise<boolean>}
     */
    async checkRateLimit() {
        const now = Date.now();
        
        // Remove old requests outside the window
        this.rateLimiter.requests = this.rateLimiter.requests.filter(
            timestamp => now - timestamp < this.rateLimiter.windowMs
        );

        // Check if we're within limits
        if (this.rateLimiter.requests.length >= this.rateLimiter.maxRequests) {
            return false;
        }

        // Add current request
        this.rateLimiter.requests.push(now);
        return true;
    }

    /**
     * Generate cache key for request
     * @param {string} zodiacSign - Zodiac sign
     * @param {string} type - Content type
     * @returns {string}
     */
    getCacheKey(zodiacSign, type) {
        const today = new Date().toISOString().split('T')[0];
        return `${zodiacSign}_${type}_${today}`;
    }

    /**
     * Get cached content
     * @param {string} cacheKey - Cache key
     * @returns {string|null}
     */
    getCachedContent(cacheKey) {
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < 24 * 60 * 60 * 1000) { // 24 hours
            return cached.content;
        }
        return null;
    }

    /**
     * Cache content
     * @param {string} cacheKey - Cache key
     * @param {string} content - Content to cache
     */
    setCachedContent(cacheKey, content) {
        this.cache.set(cacheKey, {
            content,
            timestamp: Date.now()
        });
    }

    /**
     * Make API request to OpenAI
     * @param {string} prompt - The prompt to send
     * @returns {Promise<string>}
     */
    async makeApiRequest(prompt) {
        if (!this.isAvailable()) {
            throw new Error('OpenAI service not available');
        }

        // Check rate limiting
        if (!(await this.checkRateLimit())) {
            throw new Error('Rate limit exceeded');
        }

        const requestBody = {
            model: this.model,
            messages: [{
                role: 'user',
                content: prompt
            }],
            temperature: 0.7,
            max_tokens: 1024,
            top_p: 0.95
        };

        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify(requestBody),
                timeout: 30000
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            
            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('Invalid API response format');
            }

            return data.choices[0].message.content;
        } catch (error) {
            console.error('OpenAI API request failed:', error.message);
            throw error;
        }
    }

    /**
     * Generate daily horoscope content
     * @param {string} zodiacSign - Zodiac sign
     * @param {Object} zodiacConfig - Zodiac configuration
     * @returns {Promise<Object>}
     */
    async generateDailyContent(zodiacSign, zodiacConfig) {
        const contentTypes = [
            'dailyPrediction',
            'wealthForecast', 
            'loveForecast',
            'healthForecast',
            'auspiciousTime',
            'luckyColor'
        ];

        const results = {};
        
        for (const type of contentTypes) {
            const cacheKey = this.getCacheKey(zodiacSign, type);
            
            // Try to get from cache first
            const cachedContent = this.getCachedContent(cacheKey);
            if (cachedContent) {
                results[type] = cachedContent;
                continue;
            }

            try {
                const prompt = this.generatePrompt(type, zodiacSign, zodiacConfig);
                const content = await this.makeApiRequest(prompt);
                
                results[type] = content;
                this.setCachedContent(cacheKey, content);
                
                // Add delay between requests to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.error(`Failed to generate ${type} for ${zodiacSign}:`, error.message);
                results[type] = this.getFallbackContent(type, zodiacConfig);
            }
        }

        return results;
    }

    /**
     * Generate prompt for specific content type
     * @param {string} type - Content type
     * @param {string} zodiacSign - Zodiac sign
     * @param {Object} zodiacConfig - Zodiac configuration
     * @returns {string}
     */
    generatePrompt(type, zodiacSign, zodiacConfig) {
        const today = new Date().toLocaleDateString('si-LK', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        
        const baseContext = `ඔබ ශ්‍රී ලාංකික ජ්‍යෝතිෂ විශේෂඥයෙකු වන අතර ${zodiacConfig.name} ලග්නය පිළිබඳ ගැඹුරු දැනුමක් ඇත. ${today} දිනය සඳහා`;
        
        const prompts = {
            dailyPrediction: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට විශේෂ දෛනික පුරෝකථනයක් ලියන්න. ග්‍රහ ස්ථාන, ශුභාශුභ කාල, සහ ප්‍රායෝගික උපදෙස් ඇතුළත් කරන්න. 150-200 වචන භාවිතා කරන්න.`,
            
            wealthForecast: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ගේ ධන සම්පත්, ව්‍යාපාරික අවස්ථා, ආයෝජන සහ මූල්‍ය තීරණ පිළිබඳ විස්තරාත්මක පුරෝකථනයක් ලියන්න.`,
            
            loveForecast: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ගේ ප්‍රේම සම්බන්ධතා, විවාහ ජීවිතය, පවුල් සාමය සහ සමාජ සම්බන්ධතා පිළිබඳ සවිස්තරාත්මක පුරෝකථනයක් ලියන්න.`,
            
            healthForecast: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ගේ සෞඛ්‍ය තත්වය, ශාරීරික යහපැවැත්ම, මානසික සුවතාව සහ ආහාර පාන පිළිබඳ විස්තරාත්මක පුරෝකථනයක් ලියන්න.`,
            
            auspiciousTime: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට ශුභ කාල වේලාවන්, වැදගත් කටයුතු සඳහා සුදුසු වේලාවන් සහ වර්ජනීය කාල පිළිබඳ නිශ්චිත තොරතුරු ලබා දෙන්න.`,
            
            luckyColor: `${baseContext} ${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට ශුභ වර්ණ, ඒවා භාවිතා කළ යුතු ආකාරය සහ වර්ණ ශක්තිය පිළිබඳ සවිස්තරාත්මක මාර්ගෝපදේශ ලබා දෙන්න.`
        };

        return prompts[type] || prompts.dailyPrediction;
    }

    /**
     * Get fallback content when API is not available
     * @param {string} type - Content type
     * @param {Object} zodiacConfig - Zodiac configuration
     * @returns {string}
     */
    getFallbackContent(type, zodiacConfig) {
        const fallbackContent = {
            dailyPrediction: `${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට අද දිනය ධනාත්මක අත්දැකීම් ගෙන එනු ඇත. ඔබේ ${zodiacConfig.keywords?.join(', ') || 'විශේෂ'} ගුණාංග අද විශේෂයෙන් ප්‍රබල වනු ඇත.`,
            
            wealthForecast: `ධන කටයුතුවල ප්‍රවේශම්කාරී තීරණ ගැනීමෙන් වළකින්න. පැරණි ආයෝජන සමාලෝචනය කර නව අවස්ථා සඳහා සැලසුම් කරන්න.`,
            
            loveForecast: `ප්‍රේම සම්බන්ධතාවල අවබෝධය සහ ඉවසීම වැදගත් වේ. පවුල් සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.`,
            
            healthForecast: `ශාරීරික සහ මානසික සෞඛ්‍යය සඳහා සමතුලිත ජීවන රටාවක් පවත්වන්න. ප්‍රමාණවත් විවේකය ගන්න සහ සෞඛ්‍ය සම්පන්න ආහාර ගන්න.`,
            
            auspiciousTime: `උදෑසන 6:00 - 8:00 සහ සවස 6:00 - 8:00 අතර කාල වේලාවන් ඔබට ශුභ වේ. වැදගත් කටයුතු මෙම කාල වේලාවන්හි ආරම්භ කරන්න.`,
            
            luckyColor: `අද දිනය සඳහා නිල් සහ සුදු වර්ණ ශුභ වේ. මෙම වර්ණයේ ඇඳුම් හෝ උපාංග භාවිතා කිරීමෙන් ධනාත්මක ශක්තිය ලබා ගත හැකිය.`
        };
        
        return fallbackContent[type] || fallbackContent.dailyPrediction;
    }

    /**
     * Clear cache (for maintenance)
     */
    clearCache() {
        this.cache.clear();
        console.log('OpenAI service cache cleared');
    }

    /**
     * Get service statistics
     * @returns {Object}
     */
    getStats() {
        return {
            available: this.isAvailable(),
            cacheSize: this.cache.size,
            rateLimitRequests: this.rateLimiter.requests.length,
            model: this.model
        };
    }
}

module.exports = OpenAIService;
