// Zodiac Page JavaScript with OpenAI API Integration

// Function to convert color codes to Sinhala color names
function getColorNameInSinhala(colorCode) {
    const colorMap = {
        '#FF4444': 'රතු', // Red (<PERSON><PERSON>)
        '#4CAF50': 'කොළ', // <PERSON> (Vrishabha)
        '#FFD700': 'රන්වන්', // Golden (Mithuna)
        '#87CEEB': 'අහස් නිල්', // Sky Blue (Karkata)
        '#FF8C00': 'තැඹිලි', // Orange (Simha)
        '#8FBC8F': 'ලා කොළ', // Light Green (Kanya)
        '#FFB6C1': 'රෝස', // Pink (Tula)
        '#8B0000': 'ගැඹුරු රතු', // Dark Red (Vrischika)
        '#9370DB': 'දම්', // Purple (Dhanu)
        '#2F4F4F': 'අඳුරු අළු', // Dark Slate Gray (Makara)
        '#00CED1': 'මැදගම් නිල්', // Medium Turquoise (Kumbha)
        '#20B2AA': 'මුහුදු කොළ' // Sea Green (Meena)
    };
    
    return colorMap[colorCode] || colorCode;
}

// Function to get current zodiac date range
function getCurrentZodiacDateRange(zodiacSign) {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1; // JavaScript months are 0-indexed
    
    // Define zodiac date ranges with dynamic year
    // For signs that span across years (Capricorn, Aquarius, Pisces),
    // we need to determine the correct year based on the current date
    let year = currentYear;
    
    // For the first three months of the year, if we're looking at zodiac signs
    // that start at the end of the previous year, we need to use the previous year
    // for the start date
    const isEarlyInYear = currentMonth <= 3;
    
    // Define zodiac ranges with proper year handling
    const zodiacRanges = {
        mesha: { 
            start: `${year}-03-21`, 
            end: `${year}-04-19` 
        },
        vrishabha: { 
            start: `${year}-04-20`, 
            end: `${year}-05-20` 
        },
        mithuna: { 
            start: `${year}-05-21`, 
            end: `${year}-06-20` 
        },
        karkata: { 
            start: `${year}-06-21`, 
            end: `${year}-07-22` 
        },
        simha: { 
            start: `${year}-07-23`, 
            end: `${year}-08-22` 
        },
        kanya: { 
            start: `${year}-08-23`, 
            end: `${year}-09-22` 
        },
        tula: { 
            start: `${year}-09-23`, 
            end: `${year}-10-22` 
        },
        vrischika: { 
            start: `${year}-10-23`, 
            end: `${year}-11-21` 
        },
        dhanu: { 
            start: `${year}-11-22`, 
            end: `${year}-12-21` 
        },
        makara: { 
            start: `${year}-12-22`, 
            end: `${year+1}-01-19` 
        },
        kumbha: { 
            start: isEarlyInYear ? `${year-1}-01-20` : `${year}-01-20`, 
            end: isEarlyInYear ? `${year-1}-02-18` : `${year}-02-18` 
        },
        meena: { 
            start: isEarlyInYear ? `${year-1}-02-19` : `${year}-02-19`, 
            end: isEarlyInYear ? `${year}-03-20` : `${year+1}-03-20` 
        }
    };
    
    const range = zodiacRanges[zodiacSign];
    if (!range) return '';
    
    // Format dates in Sinhala
    const startDate = new Date(range.start);
    const endDate = new Date(range.end);
    
    const months = {
        1: 'ජනවාරි',
        2: 'පෙබරවාරි',
        3: 'මාර්තු',
        4: 'අප්‍රේල්',
        5: 'මැයි',
        6: 'ජූනි',
        7: 'ජූලි',
        8: 'අගෝස්තු',
        9: 'සැප්තැම්බර්',
        10: 'ඔක්තෝබර්',
        11: 'නොවැම්බර්',
        12: 'දෙසැම්බර්'
    };
    
    return `${months[startDate.getMonth() + 1]} ${startDate.getDate()} - ${months[endDate.getMonth() + 1]} ${endDate.getDate()}`;
}

// Function to update zodiac date ranges in UI
function updateZodiacDateRanges() {
    const zodiacDates = document.querySelectorAll('.zodiac-dates');
    const currentZodiacSign = window.location.pathname.match(/\/pages\/([^\/]+)\.html$/);
    
    if (currentZodiacSign) {
        const sign = currentZodiacSign[1];
        zodiacDates.forEach(element => {
            element.textContent = getCurrentZodiacDateRange(sign);
        });
    }
}

// Zodiac data configuration
const ZODIAC_CONFIG = {
    mesha: {
        name: 'මේෂ',
        symbol: '♈',
        element: 'ගින්න',
        ruler: 'අඟහරු',
        dates: 'මාර්තු 21 - අප්‍රේල් 19',
        color: '#FF4444',
        gemstone: 'රතු කොරල්',
        luckyNumbers: [1, 8, 17],
        keywords: ['නායකත්වය', 'ධෛර්යය', 'ක්‍රියාශීලිත්වය', 'නවෝත්පාදනය']
    },
    vrishabha: {
        name: 'වෘෂභ',
        symbol: '♉',
        element: 'පෘථිවි',
        ruler: 'සිකුරු',
        dates: 'අප්‍රේල් 20 - මැයි 20',
        color: '#4CAF50',
        gemstone: 'මරකත',
        luckyNumbers: [2, 6, 9],
        keywords: ['ස්ථාවරත්වය', 'විශ්වසනීයත්වය', 'ඉවසීම', 'ප්‍රායෝගිකත්වය']
    },
    mithuna: {
        name: 'මිථුන',
        symbol: '♊',
        element: 'වායු',
        ruler: 'බුධ',
        dates: 'මැයි 21 - ජූනි 20',
        color: '#FFD700',
        gemstone: 'පන්නා',
        luckyNumbers: [5, 7, 14],
        keywords: ['සන්නිවේදනය', 'බුද්ධිමත්', 'අනුවර්තනය', 'කුතුහලය']
    },
    karkata: {
        name: 'කර්කට',
        symbol: '♋',
        element: 'ජලය',
        ruler: 'චන්ද්‍රයා',
        dates: 'ජූනි 21 - ජූලි 22',
        color: '#87CEEB',
        gemstone: 'මුතු',
        luckyNumbers: [2, 7, 11],
        keywords: ['සංවේදනය', 'රැකවරණය', 'අන්තර්ගත බුද්ධිය', 'පවුල්කාමිත්වය']
    },
    simha: {
        name: 'සිංහ',
        symbol: '♌',
        element: 'ගින්න',
        ruler: 'සූර්යයා',
        dates: 'ජූලි 23 - අගෝස්තු 22',
        color: '#FF8C00',
        gemstone: 'රුබි',
        luckyNumbers: [1, 3, 10],
        keywords: ['නායකත්වය', 'ආත්මවිශ්වාසය', 'නිර්මාණශීලිත්වය', 'ත්‍යාගශීලිත්වය']
    },
    kanya: {
        name: 'කන්‍යා',
        symbol: '♍',
        element: 'පෘථිවි',
        ruler: 'බුධ',
        dates: 'අගෝස්තු 23 - සැප්තැම්බර් 22',
        color: '#8FBC8F',
        gemstone: 'නීලමණි',
        luckyNumbers: [6, 14, 18],
        keywords: ['විශ්ලේෂණාත්මක', 'පරිපූර්ණතාවාදී', 'ප්‍රායෝගික', 'සේවාශීලී']
    },
    tula: {
        name: 'තුලා',
        symbol: '♎',
        element: 'වායු',
        ruler: 'සිකුරු',
        dates: 'සැප්තැම්බර් 23 - ඔක්තෝබර් 22',
        color: '#FFB6C1',
        gemstone: 'ඔපල්',
        luckyNumbers: [4, 6, 15],
        keywords: ['සමතුලිතතාවය', 'සාධාරණත්වය', 'සමගිය', 'රූපලාවණ්‍යය']
    },
    vrischika: {
        name: 'වෘශ්චික',
        symbol: '♏',
        element: 'ජලය',
        ruler: 'අඟහරු',
        dates: 'ඔක්තෝබර් 23 - නොවැම්බර් 21',
        color: '#8B0000',
        gemstone: 'ටොපාස්',
        luckyNumbers: [8, 11, 18],
        keywords: ['තීව්‍රතාවය', 'පරිවර්තනය', 'අභිරහස්', 'දෘඪත්වය']
    },
    dhanu: {
        name: 'ධනු',
        symbol: '♐',
        element: 'ගින්න',
        ruler: 'ගුරු',
        dates: 'නොවැම්බර් 22 - දෙසැම්බර් 21',
        color: '#9370DB',
        gemstone: 'ටර්කොයිස්',
        luckyNumbers: [3, 9, 22],
        keywords: ['ගවේෂණය', 'ප්‍රශස්තතාවය', 'දර්ශනය', 'ස්වාධීනත්වය']
    },
    makara: {
        name: 'මකර',
        symbol: '♑',
        element: 'පෘථිවි',
        ruler: 'සෙනසුරු',
        dates: 'දෙසැම්බර් 22 - ජනවාරි 19',
        color: '#2F4F4F',
        gemstone: 'ගාර්නට්',
        luckyNumbers: [6, 9, 26],
        keywords: ['අභිලාෂය', 'විනය', 'වගකීම', 'ප්‍රායෝගිකත්වය']
    },
    kumbha: {
        name: 'කුම්භ',
        symbol: '♒',
        element: 'වායු',
        ruler: 'සෙනසුරු',
        dates: 'ජනවාරි 20 - පෙබරවාරි 18',
        color: '#00CED1',
        gemstone: 'ඇමතිස්ට්',
        luckyNumbers: [4, 7, 11],
        keywords: ['නවෝත්පාදනය', 'මානවිකත්වය', 'ස්වාධීනත්වය', 'අනාගතවාදය']
    },
    meena: {
        name: 'මීන',
        symbol: '♓',
        element: 'ජලය',
        ruler: 'ගුරු',
        dates: 'පෙබරවාරි 19 - මාර්තු 20',
        color: '#20B2AA',
        gemstone: 'ඇක්වාමරීන්',
        luckyNumbers: [3, 9, 12],
        keywords: ['සංවේදනය', 'අන්තර්ගත බුද්ධිය', 'කරුණාව', 'කලාත්මකත්වය']
    }
};

// Initialize configuration
const config = new Config();
const OPENAI_API_CONFIG = config.getOpenAIConfig();

// Rate limiting variables
let lastApiCall = 0;
let apiCallCount = 0;
let apiCallResetTime = Date.now();

// Current zodiac sign
let currentZodiac = '';

// Cache for daily content
let dailyContentCache = {};

// Initialize zodiac page
function initializeZodiacPage(zodiacSign) {
    currentZodiac = zodiacSign;
    
    // Load cached content first
    loadCachedContent();
    
    // Check if content needs refresh
    if (shouldRefreshContent()) {
        refreshDailyContent();
    }
    
    // Update last refresh time display
    updateLastRefreshTime();
    
    // Update zodiac date ranges with current date
    updateZodiacDateRanges();
    
    // Add event listeners
    setupEventListeners();
}

// Setup event listeners
function setupEventListeners() {
    // Refresh button
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshDailyContent);
    }
    
    // Auto-refresh every hour
    setInterval(() => {
        if (shouldRefreshContent()) {
            refreshDailyContent();
        }
    }, 3600000); // 1 hour
}

// Check if content should be refreshed
function shouldRefreshContent() {
    const lastRefresh = localStorage.getItem(`lastRefresh_${currentZodiac}`);
    if (!lastRefresh) return true;
    
    const lastRefreshDate = new Date(lastRefresh);
    const now = new Date();
    
    // Refresh if it's a new day
    return lastRefreshDate.toDateString() !== now.toDateString();
}

// Load cached content
function loadCachedContent() {
    const cachedData = localStorage.getItem(`dailyContent_${currentZodiac}`);
    if (cachedData) {
        try {
            dailyContentCache = JSON.parse(cachedData);
            displayDailyContent(dailyContentCache);
        } catch (error) {
            console.error('Error loading cached content:', error);
            showFallbackContent();
        }
    } else {
        showFallbackContent();
    }
}

// Refresh daily content from OpenAI API with improved error handling
async function refreshDailyContent() {
    const refreshBtn = document.getElementById('refreshBtn');
    let timeoutId;
    
    // Set a timeout to detect long-running operations
    const timeout = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
            reject(new Error('Request timed out'));
        }, 15000); // 15 seconds timeout
    });
    
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<span class="loading"></span> යාවත්කාලීන වෙමින්...';
    }
    
    try {
        // Show loading state
        showLoadingState();
        
        // Check for network connectivity
        if (!navigator.onLine) {
            throw new Error('No internet connection');
        }
        
        // Get zodiac config
        const zodiacConfig = ZODIAC_CONFIG[currentZodiac];
        if (!zodiacConfig) {
            throw new Error('Invalid zodiac sign');
        }
        
        // Generate content using OpenAI API with timeout
        const dailyContent = await Promise.race([
            generateDailyContent(zodiacConfig),
            timeout
        ]);
        
        // Clear timeout if successful
        clearTimeout(timeoutId);
        
        // Cache the content
        dailyContentCache = dailyContent;
        try {
            localStorage.setItem(`dailyContent_${currentZodiac}`, JSON.stringify(dailyContent));
            localStorage.setItem(`lastRefresh_${currentZodiac}`, new Date().toISOString());
        } catch (storageError) {
            console.warn('Failed to cache content in localStorage:', storageError);
            // Continue even if storage fails
        }
        
        // Display the content
        displayDailyContent(dailyContent);
        
        // Update last refresh time
        updateLastRefreshTime();
        
        // Show success message
        showNotification('දෛනික අන්තර්ගතය සාර්ථකව යාවත්කාලීන කරන ලදී!', 'success');
        
    } catch (error) {
        // Clear timeout if there was an error
        clearTimeout(timeoutId);
        
        console.error('Error refreshing content:', error);
        
        // Show appropriate error message based on error type
        if (error.message === 'No internet connection') {
            showNotification('අන්තර්ජාල සම්බන්ධතාවය නැත. කරුණාකර ඔබේ සම්බන්ධතාවය පරීක්ෂා කර නැවත උත්සාහ කරන්න.', 'error');
        } else if (error.message === 'Request timed out') {
            showNotification('ඉල්ලීම කල් ඉකුත් විය. සේවාදායකය ප්‍රතිචාර නොදක්වයි. කරුණාකර පසුව නැවත උත්සාහ කරන්න.', 'error');
        } else if (error.message.includes('API key')) {
            showNotification('API යතුර වලංගු නැත. කරුණාකර පරිපාලක අමතන්න.', 'error');
        } else {
            showNotification('අන්තර්ගතය යාවත්කාලීන කිරීමේදී දෝෂයක් ඇතිවිය. කරුණාකර නැවත උත්සාහ කරන්න.', 'error');
        }
        
        // Show fallback content if no cached content
        if (!dailyContentCache || Object.keys(dailyContentCache).length === 0) {
            showFallbackContent();
        }
    } finally {
        // Reset refresh button
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<span class="refresh-icon">🔄</span> යාවත්කාලීන කරන්න';
        }
    }
}

// Generate daily content using OpenAI API with improved error handling and retry logic
async function generateDailyContent(zodiacConfig) {
    // Check if API key is configured
    const apiStatus = config.getApiKeyStatus();

    if (!apiStatus.isConfigured) {
        console.warn('OpenAI API key not configured:', apiStatus.message);
        console.warn('Using fallback content instead.');

        // Show user-friendly message about API key
        showApiKeyWarning(apiStatus);

        return getFallbackContentSet(zodiacConfig);
    }
    
    // Get enhanced prompts from config
    const prompts = config.getPrompts(currentZodiac, zodiacConfig);
    
    const results = {};
    const maxRetries = 2; // Maximum number of retry attempts
    const retryDelay = 2000; // Delay between retries in milliseconds
    
    // Generate content for each section
    for (const [key, prompt] of Object.entries(prompts)) {
        let retries = 0;
        let success = false;
        
        while (retries <= maxRetries && !success) {
            try {
                // If this is a retry, add a delay
                if (retries > 0) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    console.log(`Retrying ${key} (attempt ${retries})`);
                }
                
                const content = await callOpenAIAPI(prompt);
                results[key] = content;
                success = true;
            } catch (error) {
                console.error(`Error generating ${key} (attempt ${retries}):`, error);
                retries++;
                
                // If we've exhausted all retries, use fallback content
                if (retries > maxRetries) {
                    console.log(`Using fallback content for ${key} after ${maxRetries} failed attempts`);
                    results[key] = getFallbackContent(key, zodiacConfig);
                }
            }
        }
        
        // Add delay between API calls to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return results;
}

// Show API key warning to user
function showApiKeyWarning(apiStatus) {
    // Only show once per session
    if (window.apiKeyWarningShown) {
        return;
    }
    window.apiKeyWarningShown = true;

    // Update any existing API status elements
    const statusElements = document.querySelectorAll('.api-status, #api-status');
    statusElements.forEach(element => {
        element.innerHTML = `
            <div class="alert alert-warning">
                <strong>⚠️ API Key Required</strong><br>
                ${apiStatus.message}<br>
                <small>Using static content instead. <a href="https://platform.openai.com/api-keys" target="_blank">Get API Key</a></small>
            </div>
        `;
    });

    console.log('API Key Status:', apiStatus);
}

// Call OpenAI API with rate limiting and improved error handling via proxy
async function callOpenAIAPI(prompt) {
    // Check API key configuration
    const apiStatus = config.getApiKeyStatus();
    if (!apiStatus.isConfigured) {
        throw new Error(`OpenAI API key not configured: ${apiStatus.message}`);
    }

    // Log the API key (first 10 characters) for debugging
    console.log('Using API Key (first 10 chars):', OPENAI_API_CONFIG.apiKey ? OPENAI_API_CONFIG.apiKey.substring(0, 10) + '...' : 'No API key');
    console.log('API Key Status:', apiStatus);

    // Implement rate limiting
    await enforceRateLimit();
    
    const requestBody = {
        model: OPENAI_API_CONFIG.model,
        messages: [{
            role: "user",
            content: prompt
        }],
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.95
    };
    
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), OPENAI_API_CONFIG.timeout);
        
        // Use proxy server instead of direct API call
        const proxyUrl = '/api/openai';
        
        const response = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                apiKey: OPENAI_API_CONFIG.apiKey,
                requestBody: requestBody
            }),
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }));

            // Handle specific error types
            if (response.status === 401) {
                throw new Error('Invalid API key. Please check your OpenAI API key configuration.');
            } else if (response.status === 429) {
                throw new Error('API rate limit exceeded. Please try again later.');
            } else if (response.status === 500) {
                throw new Error('OpenAI API server error. Please try again later.');
            }

            throw new Error(errorData.error || `API request failed: ${response.status}`);
        }

        const data = await response.json();

        if (data.choices && data.choices[0] && data.choices[0].message) {
            return data.choices[0].message.content;
        } else if (data.error) {
            throw new Error(`API Error: ${data.error.message || data.error}`);
        } else {
            throw new Error('Invalid API response format');
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('API request timeout - please try again');
        }

        // Log detailed error information
        console.error('API call error details:', {
            message: error.message,
            stack: error.stack,
            prompt: prompt.substring(0, 100) + '...',
            apiKeyConfigured: config.isApiKeyConfigured()
        });

        throw error;
    }
}

// Enforce rate limiting for API calls
async function enforceRateLimit() {
    const now = Date.now();
    
    // Reset counter every minute
    if (now - apiCallResetTime > 60000) {
        apiCallCount = 0;
        apiCallResetTime = now;
    }
    
    // Check if we've exceeded the rate limit
    if (apiCallCount >= OPENAI_API_CONFIG.rateLimit.requestsPerMinute) {
        const waitTime = 60000 - (now - apiCallResetTime);
        console.log(`Rate limit reached. Waiting ${waitTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        apiCallCount = 0;
        apiCallResetTime = Date.now();
    }
    
    // Ensure minimum delay between requests
    const timeSinceLastCall = now - lastApiCall;
    if (timeSinceLastCall < OPENAI_API_CONFIG.rateLimit.delayBetweenRequests) {
        const waitTime = OPENAI_API_CONFIG.rateLimit.delayBetweenRequests - timeSinceLastCall;
        await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    lastApiCall = Date.now();
    apiCallCount++;
}

// Get complete fallback content set when API is not available
function getFallbackContentSet(zodiacConfig) {
    return {
        dailyPrediction: `${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට අද දිනය ධනාත්මක අත්දැකීම් ගෙන එනු ඇත. ඔබේ ${zodiacConfig.keywords.join(', ')} ගුණාංග අද විශේෂයෙන් ප්‍රබල වනු ඇත. නව අවස්ථා සඳහා සූදානම් වන්න.`,
        wealthForecast: `අද දිනය ධනාත්මක මූල්‍ය අවස්ථා ගෙන එනු ඇත. ව්‍යාපාරික කටයුතුවල සාර්ථකත්වය අපේක්ෂා කළ හැකිය.`,
        loveForecast: `ප්‍රේම සම්බන්ධතාවල සාමය සහ සතුට පවතිනු ඇත. පවුල් සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.`,
        healthForecast: `සෞඛ්‍ය තත්වය සතුටුදායක මට්ටමක පවතිනු ඇත. නිරෝගී ජීවන රටාවක් පවත්වා ගන්න.`,
        auspiciousTime: `උදෑසන 6:00 - 8:00 සහ සවස 6:00 - 8:00 අතර කාලය ශුභ වේලාවන් වේ.`,
        luckyColor: `අද දිනය සඳහා නිල් සහ සුදු වර්ණ ශුභ වේ. ඇඳුම් පැළඳුම්වල මෙම වර්ණ භාවිතා කරන්න.`
    };
}

// Get fallback content when API is not available
function getFallbackContent(type, zodiacConfig) {
    const fallbackSet = getFallbackContentSet(zodiacConfig);
    return fallbackSet[type] || `${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට අද දිනය ධනාත්මක අත්දැකීම් ගෙන එනු ඇත.`;
}

// Original fallback content function for backward compatibility
function getOriginalFallbackContent(type, zodiacConfig) {
    const fallbackContent = {
        dailyPrediction: `${zodiacConfig.name} ලග්නයේ පුද්ගලයන්ට අද දිනය ධනාත්මක අත්දැකීම් ගෙන එනු ඇත. ඔබේ ${zodiacConfig.keywords.join(', ')} ගුණාංග අද විශේෂයෙන් ප්‍රබල වනු ඇත. නව අවස්ථා සඳහා සූදානම් වන්න.`,
        
        wealthForecast: `ධන කටයුතුවල ප්‍රවේශම්කාරී තීරණ ගැනීමෙන් වළකින්න. පැරණි ආයෝජන සමාලෝචනය කර නව අවස්ථා සඳහා සැලසුම් කරන්න. ${zodiacConfig.element} මූලද්‍රව්‍යයේ ශක්තිය ඔබට උපකාර කරනු ඇත.`,
        
        loveForecast: `ප්‍රේම සම්බන්ධතාවල අවබෝධය සහ ඉවසීම වැදගත් වේ. පවුල් සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න. ${zodiacConfig.ruler} ග්‍රහයාගේ බලපෑම ඔබේ සම්බන්ධතා ශක්තිමත් කරනු ඇත.`,
        
        healthForecast: `ශාරීරික සහ මානසික සෞඛ්‍යය සඳහා සමතුලිත ජීවන රටාවක් පවත්වන්න. ප්‍රමාණවත් විවේකය ගන්න සහ සෞඛ්‍ය සම්පන්න ආහාර ගන්න. ධ්‍යානය හෝ යෝගා අභ්‍යාස කිරීම හිතකර වනු ඇත.`,
        
        auspiciousTime: `උදෑසන 6:00 - 8:00 සහ සවස 6:00 - 8:00 අතර කාල වේලාවන් ඔබට ශුභ වේ. වැදගත් කටයුතු මෙම කාල වේලාවන්හි ආරම්භ කරන්න.`,
        
        luckyColor: `අද දිනය සඳහා ${getColorNameInSinhala(zodiacConfig.color)} වර්ණය ඔබට ශුභ වේ. මෙම වර්ණයේ ඇඳුම් හෝ උපාංග භාවිතා කිරීමෙන් ධනාත්මක ශක්තිය ලබා ගත හැකිය.`
    };
    
    return fallbackContent[type] || 'අන්තර්ගතය ලබා ගත නොහැකි විය.';
}

// Display daily content
function displayDailyContent(content) {
    const contentElements = {
        dailyPrediction: document.getElementById('dailyPrediction'),
        wealthForecast: document.getElementById('wealthForecast'),
        loveForecast: document.getElementById('loveForecast'),
        healthForecast: document.getElementById('healthForecast'),
        auspiciousTime: document.getElementById('auspiciousTime'),
        luckyColor: document.getElementById('luckyColor')
    };
    
    Object.entries(contentElements).forEach(([key, element]) => {
        if (element && content[key]) {
            element.innerHTML = `<p>${content[key]}</p>`;
            element.classList.remove('fade-in'); // Remove existing class if any
            element.style.opacity = '1'; // Ensure content is visible
            element.style.transform = 'translateY(0)'; // Reset any transform
            
            // Force a reflow before adding the animation class
            void element.offsetWidth;
            
            // Add the animation class
            element.classList.add('fade-in');
        }
    });
}

// Show loading state
function showLoadingState() {
    const loadingElements = [
        'dailyPrediction', 'wealthForecast', 'loveForecast',
        'healthForecast', 'auspiciousTime', 'luckyColor'
    ];
    
    loadingElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = '<div class="loading-content"><div class="loading"></div> Loading...</div>';
        }
    });
}

// Show fallback content when API is not available
function showFallbackContent() {
    const zodiacConfig = ZODIAC_CONFIG[currentZodiac];
    if (!zodiacConfig) return;
    
    const fallbackContent = {
        dailyPrediction: getFallbackContent('dailyPrediction', zodiacConfig),
        wealthForecast: getFallbackContent('wealthForecast', zodiacConfig),
        loveForecast: getFallbackContent('loveForecast', zodiacConfig),
        healthForecast: getFallbackContent('healthForecast', zodiacConfig),
        auspiciousTime: getFallbackContent('auspiciousTime', zodiacConfig),
        luckyColor: getFallbackContent('luckyColor', zodiacConfig)
    };
    
    displayDailyContent(fallbackContent);
}

// Update last refresh time display
function updateLastRefreshTime() {
    const updateTimeElement = document.getElementById('updateTime');
    if (updateTimeElement) {
        const lastRefresh = localStorage.getItem(`lastRefresh_${currentZodiac}`);
        if (lastRefresh) {
            const date = new Date(lastRefresh);
            updateTimeElement.textContent = date.toLocaleString('si-LK');
        } else {
            updateTimeElement.textContent = 'කිසි විටෙකත් නැත';
        }
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;
    
    // Set background color based on type
    const colors = {
        success: '#d4edda',
        error: '#f8d7da',
        info: '#d1ecf1',
        warning: '#fff3cd'
    };
    
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Add CSS for notifications
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
    }
    
    .notification-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s;
    }
    
    .notification-close:hover {
        opacity: 1;
    }
    
    .fade-in {
        animation: fadeIn 0.5s ease;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(notificationStyles);

// Export functions for global use
window.initializeZodiacPage = initializeZodiacPage;
window.refreshDailyContent = refreshDailyContent;
window.ZODIAC_CONFIG = ZODIAC_CONFIG;

// Update all zodiac cards on index page
function updateAllZodiacCards() {
    const zodiacCards = document.querySelectorAll('.zodiac-card');
    
    zodiacCards.forEach(card => {
        const sign = card.getAttribute('data-sign');
        if (sign && ZODIAC_CONFIG[sign]) {
            const dateElement = card.querySelector('.card-front p');
            if (dateElement) {
                dateElement.textContent = getCurrentZodiacDateRange(sign);
            }
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Auto-detect zodiac sign from URL or page content
    const path = window.location.pathname;
    
    // Check if this is a zodiac detail page
    const zodiacMatch = path.match(/\/pages\/(\w+)\.html$/);
    if (zodiacMatch) {
        const zodiacSign = zodiacMatch[1];
        if (ZODIAC_CONFIG[zodiacSign]) {
            initializeZodiacPage(zodiacSign);
        }
    } 
    // Check if this is the index page with all zodiac cards
    else if (path === '/' || path.endsWith('index.html')) {
        updateAllZodiacCards();
    }
});

// Service Worker for offline functionality
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
        .then(registration => {
            console.log('Service Worker registered successfully');
        })
        .catch(error => {
            console.log('Service Worker registration failed');
        });
}