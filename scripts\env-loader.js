/**
 * Environment Loader for Kubera Manthra
 * This file is kept for backward compatibility
 * The actual environment configuration is now handled by env-config.js
 */

console.warn('env-loader.js is deprecated. Please use env-config.js instead.');

// Redirect to the new environment configuration
if (typeof window !== 'undefined' && window.ENV_CONFIG) {
    // Environment is already loaded by env-config.js
    console.log('Environment configuration loaded successfully');
} else {
    // Load the new environment configuration
    const script = document.createElement('script');
    script.src = '/scripts/env-config.js';
    script.onload = function() {
        console.log('Environment configuration loaded via fallback');
    };
    script.onerror = function() {
        console.error('Failed to load environment configuration');
    };
    document.head.appendChild(script);
}
