// Environment variable loader for browser environments
// This script reads environment variables and makes them available to the browser

(function() {
    'use strict';
    
    // Initialize global ENV object
    window.ENV = window.ENV || {};
    
    // Function to load environment variables from .env file
    async function loadEnvFile() {
        try {
            const response = await fetch('./.env');
            if (!response.ok) {
                console.warn('Could not load .env file:', response.status);
                return;
            }
            
            const envContent = await response.text();
            parseEnvContent(envContent);
            
        } catch (error) {
            console.warn('Error loading .env file:', error);
            // Fallback: try to load from localStorage or prompt user
            loadFromAlternativeSources();
        }
    }
    
    // Parse .env file content
    function parseEnvContent(content) {
        const lines = content.split('\n');
        
        lines.forEach(line => {
            // Skip empty lines and comments
            line = line.trim();
            if (!line || line.startsWith('#')) {
                return;
            }
            
            // Parse KEY=VALUE format
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0) {
                const key = line.substring(0, equalIndex).trim();
                const value = line.substring(equalIndex + 1).trim();
                
                // Remove quotes if present
                const cleanValue = value.replace(/^["']|["']$/g, '');
                
                window.ENV[key] = cleanValue;
            }
        });
        
        console.log('Environment variables loaded successfully');
    }
    
    // Load from alternative sources if .env file is not available
    function loadFromAlternativeSources() {
        // Try localStorage first
        const storedApiKey = localStorage.getItem('OPENAI_API_KEY');
        if (storedApiKey) {
            window.ENV.OPENAI_API_KEY = storedApiKey;
            console.log('API key loaded from localStorage');
            return;
        }
        
        // If no API key found, show setup instructions
        console.warn('No OpenAI API key found. Please configure your API key.');
        showApiKeySetupInstructions();
    }
    
    // Show API key setup instructions
    function showApiKeySetupInstructions() {
        // Create a subtle notification for API key setup
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 10000;
            font-family: 'Noto Sans Sinhala', sans-serif;
            font-size: 14px;
            line-height: 1.4;
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <span style="font-size: 18px; margin-right: 8px;">⚠️</span>
                <strong>API යතුර අවශ්‍යයි</strong>
            </div>
            <p style="margin: 0 0 10px 0; color: #856404;">
                සම්පූර්ණ අත්දැකීම සඳහා OpenAI API යතුරක් අවශ්‍යයි.
            </p>
            <button id="setupApiKey" style="
                background: #007bff;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                margin-right: 8px;
            ">API යතුර සකසන්න</button>
            <button id="closeNotification" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            ">වසන්න</button>
        `;
        
        document.body.appendChild(notification);
        
        // Add event listeners
        document.getElementById('setupApiKey').addEventListener('click', () => {
            promptForApiKey();
            document.body.removeChild(notification);
        });
        
        document.getElementById('closeNotification').addEventListener('click', () => {
            document.body.removeChild(notification);
        });
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 10000);
    }
    
    // Prompt user for API key
    function promptForApiKey() {
        const apiKey = prompt(
            'කරුණාකර ඔබේ OpenAI API යතුර ඇතුළත් කරන්න:\n\n' +
            'API යතුරක් ලබා ගැනීම සඳහා:\n' +
             '1. https://platform.openai.com/api-keys වෙත යන්න\n' +
             '2. නව API යතුරක් සාදන්න\n' +
             '3. යතුර පිටපත් කර මෙහි ඇතුළත් කරන්න'
        );
        
        if (apiKey && apiKey.trim()) {
            const cleanApiKey = apiKey.trim();
            window.ENV.OPENAI_API_KEY = cleanApiKey;
            localStorage.setItem('OPENAI_API_KEY', cleanApiKey);
            
            // Show success message
            alert('API යතුර සාර්ථකව සකසන ලදී! පිටුව නැවත පූරණය වනු ඇත.');
            
            // Reload the page to apply the new API key
            window.location.reload();
        }
    }
    
    // Load environment variables when the script loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadEnvFile);
    } else {
        loadEnvFile();
    }
    
})();