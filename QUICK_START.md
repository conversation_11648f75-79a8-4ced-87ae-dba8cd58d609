# Kubera Manthra - Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Setup Environment
```bash
npm run setup
```
This creates a `.env` file. Edit it and add your OpenAI API key:
```env
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

### Step 3: Start the Server
```bash
npm start
```

### Step 4: Open Your Browser
Visit: http://localhost:3001

## 🔑 Getting Your OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in or create an account
3. Click "Create new secret key"
4. Copy the key (starts with `sk-`)
5. Paste it in your `.env` file

## 🧪 Test Your Setup

### Check if server is running:
```bash
npm run health
```

### Test API endpoints:
```bash
# Health check
curl http://localhost:3001/api/health

# Get zodiac signs
curl http://localhost:3001/api/zodiac-signs

# Get daily horoscope (example)
curl http://localhost:3001/api/daily-horoscope/mesha
```

## 📱 Using the Application

### Main Features:
- **Homepage**: Browse all 12 zodiac signs
- **Zodiac Pages**: Get detailed daily horoscopes
- **Auto-refresh**: Content updates daily
- **Offline Support**: Works even without internet
- **Fallback Content**: Always shows content, even if API is down

### Navigation:
1. Start at the homepage (index.html)
2. Click any zodiac card to view detailed horoscope
3. Use the refresh button to get updated content
4. Content is cached for 24 hours for better performance

## 🛠️ Development Commands

```bash
# Start development server
npm run dev

# Start production server
npm run prod

# Check service health
npm run health

# Clear server cache
npm run clear-cache
```

## 🔧 Troubleshooting

### Server won't start?
- Make sure Node.js is installed (version 14+)
- Run `npm install` to install dependencies
- Check if port 3001 is available

### API not working?
- Verify your OpenAI API key in `.env` file
- Check your internet connection
- Run `npm run health` to check service status
- The app will show fallback content if API is unavailable

### Content not updating?
- Click the refresh button on zodiac pages
- Clear cache with `npm run clear-cache`
- Content automatically refreshes daily

## 📁 Important Files

- `.env` - Your configuration (API key, etc.)
- `server.js` - Main server file
- `package.json` - Dependencies and scripts
- `scripts/api-client.js` - Client-side API handling
- `services/openai-service.js` - Server-side OpenAI integration

## 🌟 Features

### ✅ What Works Now:
- Secure OpenAI API integration
- Daily horoscope generation in Sinhala
- Intelligent caching system
- Fallback content when API unavailable
- Responsive design for all devices
- Service worker for offline functionality

### 🔒 Security Features:
- API keys stored securely on server
- No sensitive data exposed to client
- CORS protection
- Input validation
- Rate limiting

### ⚡ Performance Features:
- 24-hour content caching
- Optimized API calls
- Fast fallback content
- Efficient error handling
- Automatic retry logic

## 📞 Need Help?

1. Check the `MIGRATION_SUMMARY.md` for detailed technical information
2. Review the `README.md` for complete documentation
3. Look at server logs for error messages
4. Test API endpoints individually to isolate issues

## 🎯 Next Steps

Once you have the basic setup working:

1. **Customize Content**: Modify fallback content in `services/openai-service.js`
2. **Styling**: Update CSS files in the `styles/` directory
3. **Add Features**: Extend the API with new endpoints
4. **Deploy**: Configure for production deployment
5. **Monitor**: Set up logging and monitoring

---

**Enjoy your modernized Kubera Manthra application! 🌟**
