// Client-side environment configuration
// This file handles client-side environment setup without exposing sensitive data

/**
 * Client Environment Configuration
 * Handles API configuration and client-side environment setup
 */
class ClientEnvironment {
    constructor() {
        this.apiEndpoint = '/api/daily-horoscope';
        this.fallbackMode = false;
        this.initialized = false;
        this.init();
    }

    /**
     * Initialize client environment
     */
    init() {
        if (this.initialized) return;

        // Set up global environment object
        if (typeof window !== 'undefined') {
            window.ENV = window.ENV || {};
            window.ENV.API_ENDPOINT = this.apiEndpoint;
            window.ENV.FALLBACK_MODE = this.fallbackMode;
        }

        this.initialized = true;
        console.log('Client environment initialized');
    }

    /**
     * Check if API service is available
     * @returns {Promise<boolean>}
     */
    async checkApiAvailability() {
        try {
            const response = await fetch('/api/health');
            return response.ok;
        } catch (error) {
            console.warn('API service not available, using fallback mode');
            this.fallbackMode = true;
            return false;
        }
    }

    /**
     * Get API endpoint for horoscope data
     * @returns {string}
     */
    getApiEndpoint() {
        return this.apiEndpoint;
    }

    /**
     * Check if running in fallback mode
     * @returns {boolean}
     */
    isFallbackMode() {
        return this.fallbackMode;
    }
}

// Initialize client environment
const clientEnv = new ClientEnvironment();

/**
 * Show service status notification
 * @param {string} status - Service status ('available', 'fallback', 'error')
 * @param {string} message - Status message
 */
function showServiceStatus(status = 'available', message = '') {
    // Only show if we're in a browser environment and not in an iframe
    if (typeof window === 'undefined' || window.parent !== window) {
        return;
    }

    // Remove existing notifications
    const existingNotification = document.getElementById('service-status-notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Don't show notification if service is available
    if (status === 'available') {
        return;
    }

    const notification = document.createElement('div');
    notification.id = 'service-status-notification';

    const statusConfig = {
        fallback: {
            color: '#ff9800',
            icon: '⚠️',
            title: 'Fallback Mode',
            defaultMessage: 'Using static content. AI features temporarily unavailable.'
        },
        error: {
            color: '#f44336',
            icon: '❌',
            title: 'Service Error',
            defaultMessage: 'Service temporarily unavailable. Please try again later.'
        }
    };

    const config = statusConfig[status] || statusConfig.fallback;
    const displayMessage = message || config.defaultMessage;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${config.color};
        color: white;
        padding: 12px 16px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        z-index: 10000;
        max-width: 320px;
        font-family: 'Noto Sans Sinhala', sans-serif;
        font-size: 13px;
        line-height: 1.3;
        animation: slideInRight 0.3s ease;
    `;

    notification.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 8px;">
            <div>
                <strong>${config.icon} ${config.title}</strong><br>
                <span style="opacity: 0.9;">${displayMessage}</span>
            </div>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; opacity: 0.8;">×</button>
        </div>
    `;

    // Add animation styles
    if (!document.getElementById('notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }

    document.body.appendChild(notification);

    // Auto-hide after 8 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideInRight 0.3s ease reverse';
            setTimeout(() => notification.remove(), 300);
        }
    }, 8000);
}

// Export for global use
if (typeof window !== 'undefined') {
    window.ClientEnvironment = ClientEnvironment;
    window.showServiceStatus = showServiceStatus;
}