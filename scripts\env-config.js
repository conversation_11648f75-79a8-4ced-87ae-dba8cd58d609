// Environment configuration for API keys
// This file should be replaced with proper environment variable loading in production

// Set the OpenAI API key - using the correct format for OpenAI API keys
// Standard OpenAI API keys start with 'sk-' followed by a string of characters, not 'sk-proj-'
const OPENAI_API_KEY = '***************************************************************************************************************************************************************'; // Modified to use standard OpenAI API key format

// Make it available globally
if (typeof window !== 'undefined') {
    // Create window.ENV if it doesn't exist
    window.ENV = window.ENV || {};
    
    // Set the API key
    window.ENV.OPENAI_API_KEY = OPENAI_API_KEY;
    
    // Also set in localStorage for persistence
    localStorage.setItem('OPENAI_API_KEY', OPENAI_API_KEY);
    
    // Log for debugging
    console.log('env-config.js: API Key set in window.ENV and localStorage');
    console.log('env-config.js: API Key (first 10 chars):', OPENAI_API_KEY.substring(0, 10) + '...');
}