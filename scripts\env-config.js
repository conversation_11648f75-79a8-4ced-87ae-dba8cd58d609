// Environment configuration for API keys
// This file should be replaced with proper environment variable loading in production

// IMPORTANT: Replace this with your actual OpenAI API key
// Get your API key from: https://platform.openai.com/api-keys
// Standard OpenAI API keys start with 'sk-' followed by 48 characters
const OPENAI_API_KEY = '********************************************************************************************************************************************************************'; // Replace with your actual API key

// Validate API key format
function isValidApiKeyFormat(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
        return false;
    }

    // Check if it's the placeholder
    if (apiKey === 'YOUR_OPENAI_API_KEY_HERE') {
        return false;
    }

    // Check basic format: starts with 'sk-' and has reasonable length
    return apiKey.startsWith('sk-') && apiKey.length >= 20;
}

// Make it available globally
if (typeof window !== 'undefined') {
    // Create window.ENV if it doesn't exist
    window.ENV = window.ENV || {};

    // Validate and set the API key
    if (isValidApiKeyFormat(OPENAI_API_KEY)) {
        window.ENV.OPENAI_API_KEY = OPENAI_API_KEY;
        localStorage.setItem('OPENAI_API_KEY', OPENAI_API_KEY);
        console.log('env-config.js: Valid API Key configured');
        console.log('env-config.js: API Key (first 10 chars):', OPENAI_API_KEY.substring(0, 10) + '...');
    } else {
        console.warn('env-config.js: Invalid or placeholder API key detected');
        console.warn('env-config.js: Please replace YOUR_OPENAI_API_KEY_HERE with your actual OpenAI API key');
        console.warn('env-config.js: Get your API key from: https://platform.openai.com/api-keys');

        // Don't set invalid keys
        window.ENV.OPENAI_API_KEY = null;
        localStorage.removeItem('OPENAI_API_KEY');

        // Show user-friendly setup instructions
        showApiKeySetupInstructions();
    }
}

// Show setup instructions to user
function showApiKeySetupInstructions() {
    // Only show if we're in a browser environment and not in an iframe
    if (typeof window !== 'undefined' && window.parent === window) {
        setTimeout(() => {
            const message = `
🔑 OpenAI API Key Setup Required

To use the AI-powered features of Kubera Manthra, you need to configure your OpenAI API key.

Steps:
1. Visit: https://platform.openai.com/api-keys
2. Create a new API key
3. Copy the key
4. Replace 'YOUR_OPENAI_API_KEY_HERE' in scripts/env-config.js with your actual key

The app will work with static content until the API key is configured.
            `;

            console.log(message);

            // Show a non-intrusive notification
            if (document.body) {
                showApiKeyNotification();
            } else {
                document.addEventListener('DOMContentLoaded', showApiKeyNotification);
            }
        }, 1000);
    }
}

// Show API key setup notification
function showApiKeyNotification() {
    // Check if notification already exists
    if (document.getElementById('api-key-notification')) {
        return;
    }

    const notification = document.createElement('div');
    notification.id = 'api-key-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff6b35;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 350px;
        font-family: 'Noto Sans Sinhala', sans-serif;
        font-size: 14px;
        line-height: 1.4;
    `;

    notification.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div>
                <strong>🔑 API Key Required</strong><br>
                OpenAI API key not configured. <a href="https://platform.openai.com/api-keys" target="_blank" style="color: #fff; text-decoration: underline;">Get API Key</a>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin-left: 10px;">×</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 10000);
}