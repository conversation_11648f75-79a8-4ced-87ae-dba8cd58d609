// Service Worker for Kubera Manthra Website

const CACHE_NAME = 'kubera-manthra-v2';
const STATIC_CACHE_NAME = 'kubera-manthra-static-v2';
const API_CACHE_NAME = 'kubera-manthra-api-v2';

// Resources to cache initially
const INITIAL_CACHE_URLS = [
  '/',
  '/index.html',
  '/store.html',
  '/styles/main.css',
  '/styles/zodiac.css',
  '/styles/store.css',
  '/scripts/main.js',
  '/scripts/zodiac.js',
  '/scripts/api-client.js',
  '/scripts/env-config.js',
  // Zodiac pages
  '/pages/mesha.html',
  '/pages/vrishabha.html',
  '/pages/mithuna.html',
  '/pages/karkata.html',
  '/pages/simha.html',
  '/pages/kanya.html',
  '/pages/tula.html',
  '/pages/vrischika.html',
  '/pages/dhanu.html',
  '/pages/makara.html',
  '/pages/kumbha.html',
  '/pages/meena.html'
];

// Install event - cache initial resources
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');

  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching initial resources');
        // Cache files one by one to avoid failures
        return Promise.allSettled(
          INITIAL_CACHE_URLS.map(url => {
            const request = new Request(url, { cache: 'reload' });
            return cache.add(request).catch(error => {
              console.warn(`Service Worker: Failed to cache ${url}:`, error);
              return null; // Continue with other files
            });
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Installation complete');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Installation failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');

  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== STATIC_CACHE_NAME &&
              cacheName !== API_CACHE_NAME &&
              cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Activation complete');
      return self.clients.claim();
    })
  );
});

// Fetch event - serve from cache or network with improved error handling
self.addEventListener('fetch', event => {
  const request = event.request;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static file requests
  event.respondWith(handleStaticRequest(request));
});

// Handle API requests
async function handleApiRequest(request) {
  try {
    // Try network first for API requests
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful API responses
      const cache = await caches.open(API_CACHE_NAME);
      cache.put(request, networkResponse.clone()).catch(err => {
        console.warn('Service Worker: Failed to cache API response:', err);
      });
    }

    return networkResponse;

  } catch (error) {
    console.warn('Service Worker: API request failed:', error);

    // Try to serve from cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return error response
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Service unavailable',
        message: 'API is currently unavailable. Please try again later.'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// Handle static file requests
async function handleStaticRequest(request) {
  try {
    // Try cache first for static files
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Try network
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone()).catch(err => {
        console.warn('Service Worker: Failed to cache static file:', err);
      });
    }

    return networkResponse;

  } catch (error) {
    console.warn('Service Worker: Static request failed:', error);

    // Try cache again
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // For navigation requests, serve the index page as fallback
    if (request.mode === 'navigate') {
      const indexResponse = await caches.match('/index.html');
      if (indexResponse) {
        return indexResponse;
      }
    }

    // Return error response
    return new Response(
      'Network error occurred. Please check your connection.',
      {
        status: 503,
        headers: { 'Content-Type': 'text/plain' }
      }
    );
  }
}

// Background sync for offline content updates
self.addEventListener('sync', event => {
  if (event.tag === 'sync-daily-content') {
    event.waitUntil(syncDailyContent());
  }
});

// Function to sync daily content when back online
async function syncDailyContent() {
  // Get all clients
  const clients = await self.clients.matchAll();
  
  // Send message to clients to refresh content
  clients.forEach(client => {
    client.postMessage({
      type: 'SYNC_DAILY_CONTENT'
    });
  });
}

// Handle messages from clients
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'CACHE_NEW_ROUTE') {
    // Add new routes to cache
    caches.open(CACHE_NAME)
      .then(cache => cache.add(event.data.url));
  }
});