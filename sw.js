// Service Worker for Kubera Manthra Website

const CACHE_NAME = 'kubera-manthra-v1';

// Resources to cache initially
const INITIAL_CACHE_URLS = [
  '/',
  '/index.html',
  '/store.html',
  '/styles/main.css',
  '/styles/zodiac.css',
  '/styles/store.css',
  '/scripts/main.js',
  '/scripts/zodiac.js',
  '/scripts/store.js',
  // Zodiac pages
  '/pages/mesha.html',
  '/pages/vrishabha.html',
  '/pages/mithuna.html',
  '/pages/karkata.html',
  '/pages/simha.html',
  '/pages/kanya.html',
  '/pages/tula.html',
  '/pages/vrischika.html',
  '/pages/dhanu.html',
  '/pages/makara.html',
  '/pages/kumbha.html',
  '/pages/meena.html',
  // Fonts
  'https://fonts.googleapis.com/css2?family=Noto+Sans+Sinhala:wght@300;400;500;600;700&display=swap'
];

// Install event - cache initial resources
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching initial resources');
        return cache.addAll(INITIAL_CACHE_URLS.map(url => {
          // Convert relative URLs to absolute
          return url.startsWith('http') ? url : new URL(url, self.location.origin).href;
        }));
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache or network with improved error handling
self.addEventListener('fetch', event => {
  // Skip non-GET requests and API calls
  if (event.request.method !== 'GET' || 
      event.request.url.includes('generativelanguage.googleapis.com')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(cachedResponse => {
        // Return cached response if available
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise fetch from network
        return fetch(event.request)
          .then(response => {
            // Don't cache if response is not valid
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response to cache it and return it
            const responseToCache = response.clone();
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              })
              .catch(err => {
                console.error('Cache put error:', err);
                // Continue even if caching fails
              });

            return response;
          })
          .catch(error => {
            console.error('Fetch error:', error);
            
            // For navigation requests, serve the index page as fallback
            if (event.request.mode === 'navigate') {
              return caches.match('/index.html')
                .catch(err => {
                  console.error('Fallback error:', err);
                  return new Response('Network error occurred. Please check your connection.', {
                    status: 503,
                    headers: { 'Content-Type': 'text/plain' }
                  });
                });
            }
            
            // Otherwise, return a network error response
            return new Response('Network error occurred. Please check your connection.', {
              status: 503,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});

// Background sync for offline content updates
self.addEventListener('sync', event => {
  if (event.tag === 'sync-daily-content') {
    event.waitUntil(syncDailyContent());
  }
});

// Function to sync daily content when back online
async function syncDailyContent() {
  // Get all clients
  const clients = await self.clients.matchAll();
  
  // Send message to clients to refresh content
  clients.forEach(client => {
    client.postMessage({
      type: 'SYNC_DAILY_CONTENT'
    });
  });
}

// Handle messages from clients
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'CACHE_NEW_ROUTE') {
    // Add new routes to cache
    caches.open(CACHE_NAME)
      .then(cache => cache.add(event.data.url));
  }
});