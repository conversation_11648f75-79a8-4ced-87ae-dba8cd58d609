/* Store-specific styles */

/* Store Hero Section */
.store-hero {
    height: 60vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #4a148c 0%, #7b1fa2 50%, #ad1457 100%);
    overflow: hidden;
    margin-top: 70px;
    padding: 1rem;
}

.store-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #ffd700;
    text-align: center;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.store-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

/* Media queries for store page */
@media (max-width: 768px) {
    .store-title {
        font-size: 2.8rem;
    }
    
    .store-subtitle {
        font-size: 1.1rem;
        padding: 0 1rem;
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .category-card {
        padding: 1.5rem;
    }
    
    .cart-icon {
        position: absolute;
        right: 60px;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 0;
        display: flex;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .store-hero {
        height: 50vh;
    }
    
    .store-title {
        font-size: 2.2rem;
    }
    
    .store-subtitle {
        font-size: 1rem;
    }
    
    .category-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .category-card {
        padding: 1.2rem;
    }
}

/* Cart Icon */
.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 1rem;
    z-index: 1002;
}

.cart-icon:hover {
    background: rgba(255, 215, 0, 0.2);
}

.cart-symbol {
    font-size: 1.5rem;
    color: #ffd700;
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Categories Section */
.categories {
    padding: 4rem 0;
    background: #f8f9fa;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: #ffd700;
}

.category-card.active {
    border-color: #1a237e;
    background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
    color: white;
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.category-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1a237e;
}

.category-card.active h3 {
    color: #ffd700;
}

.category-card p {
    color: #666;
    line-height: 1.6;
}

.category-card.active p {
    color: rgba(255, 255, 255, 0.9);
}

/* Products Section */
.products {
    padding: 4rem 0;
    background: white;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select {
    padding: 0.8rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-family: 'Noto Sans Sinhala', sans-serif;
    font-size: 1rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-controls select:focus {
    outline: none;
    border-color: #1a237e;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.product-image {
    width: 100%;
    height: 250px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    position: relative;
    overflow: hidden;
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff4444;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.product-badge.new {
    background: #4caf50;
}

.product-badge.sale {
    background: #ff9800;
}

.product-info {
    padding: 1.5rem;
}

.product-category {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.product-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a237e;
}

.original-price {
    font-size: 1.1rem;
    color: #999;
    text-decoration: line-through;
}

.discount {
    background: #ff4444;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-add-cart {
    flex: 1;
    background: linear-gradient(45deg, #1a237e, #3f51b5);
    color: white;
    border: none;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Noto Sans Sinhala', sans-serif;
}

.btn-add-cart:hover {
    background: linear-gradient(45deg, #3f51b5, #5c6bc0);
    transform: translateY(-2px);
}

.btn-view-details {
    background: transparent;
    color: #1a237e;
    border: 2px solid #1a237e;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Noto Sans Sinhala', sans-serif;
}

.btn-view-details:hover {
    background: #1a237e;
    color: white;
}

/* Shopping Cart Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open {
    right: 0;
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1a237e;
    color: white;
}

.cart-header h3 {
    margin: 0;
    color: #ffd700;
}

.close-cart {
    background: none;
    border: none;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-weight: 600;
    color: #1a237e;
    margin-bottom: 0.3rem;
    font-size: 0.95rem;
}

.cart-item-price {
    color: #666;
    font-size: 0.9rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: #1a237e;
    color: white;
    border-color: #1a237e;
}

.quantity-display {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
}

.remove-item {
    background: #ff4444;
    color: white;
    border: none;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.cart-total {
    text-align: center;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: #1a237e;
}

.checkout-btn {
    width: 100%;
    background: linear-gradient(45deg, #ffd700, #ffeb3b);
    color: #1a237e;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Noto Sans Sinhala', sans-serif;
}

.checkout-btn:hover {
    background: linear-gradient(45deg, #ffeb3b, #ffc107);
    transform: translateY(-2px);
}

.checkout-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

.empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-cart-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Product Modal */
.product-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.product-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    margin: 1rem;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 2rem;
}

.modal-product-image {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 6rem;
    margin-bottom: 2rem;
}

.modal-product-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a237e;
    margin-bottom: 1rem;
}

.modal-product-description {
    color: #666;
    line-height: 1.8;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.modal-product-features {
    margin-bottom: 2rem;
}

.modal-product-features h4 {
    color: #1a237e;
    margin-bottom: 1rem;
}

.modal-product-features ul {
    list-style: none;
    padding: 0;
}

.modal-product-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-product-features li:before {
    content: "✓";
    color: #4caf50;
    font-weight: bold;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.5rem;
}

.quantity-selector button {
    width: 35px;
    height: 35px;
    border: none;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.quantity-selector button:hover {
    background: #1a237e;
    color: white;
}

.quantity-selector input {
    width: 60px;
    text-align: center;
    border: none;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .store-title {
        font-size: 2.5rem;
    }
    
    .store-subtitle {
        font-size: 1.1rem;
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .products-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .cart-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .modal-content {
        margin: 0.5rem;
        max-height: 95vh;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

@media (max-width: 480px) {
    .store-hero {
        height: 50vh;
    }
    
    .store-title {
        font-size: 2rem;
    }
    
    .category-card {
        padding: 1.5rem;
    }
    
    .product-card {
        margin-bottom: 1rem;
    }
    
    .product-actions {
        flex-direction: column;
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.product-card.loading {
    pointer-events: none;
}

.product-card.loading .product-image,
.product-card.loading .product-title,
.product-card.loading .product-description,
.product-card.loading .product-price {
    background: #f0f0f0;
    color: transparent;
    border-radius: 4px;
}

/* Success/Error States */
.success-message {
    background: #4caf50;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    text-align: center;
    animation: slideDown 0.3s ease;
}

.error-message {
    background: #f44336;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    text-align: center;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}