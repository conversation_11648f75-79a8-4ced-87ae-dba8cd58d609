<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubera Manthra - System Status</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #ddd;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-card h3 {
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-card p {
            margin: 5px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #1e7e34;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .code {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .nav-link {
            display: inline-block;
            margin: 10px 0;
            color: #007bff;
            text-decoration: none;
        }
        
        .nav-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 System Status</h1>
            <p>Kubera Manthra Configuration Dashboard</p>
        </div>
        
        <div class="content">
            <a href="/" class="nav-link">← Back to Main Site</a>
            
            <div id="serverStatus" class="status-card">
                <h3>🖥️ Server Status</h3>
                <p>Checking server connection...</p>
            </div>
            
            <div id="apiKeyStatus" class="status-card">
                <h3>🔑 API Key Configuration</h3>
                <p>Checking API key status...</p>
            </div>
            
            <div id="apiTestStatus" class="status-card">
                <h3>🧪 API Functionality Test</h3>
                <p>Ready to test API functionality</p>
                <button id="testApiBtn" class="btn">Test API Key</button>
            </div>
            
            <div id="setupInstructions" class="status-card warning" style="display: none;">
                <h3>⚙️ Setup Instructions</h3>
                <p><strong>To configure your Google Gemini API key:</strong></p>
                <ol>
                    <li>Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a></li>
                    <li>Set your environment variable:</li>
                </ol>
                <div class="code">GEMINI_API_KEY=your_api_key_here</div>
                <p>Or create a <code>.env</code> file in the project root with your Gemini API key.</p>
            </div>
            
            <div class="status-card">
                <h3>📊 System Information</h3>
                <p><strong>Server:</strong> <span id="serverInfo">Loading...</span></p>
                <p><strong>Last Updated:</strong> <span id="lastUpdated">Loading...</span></p>
                <p><strong>Browser:</strong> <span id="browserInfo">Loading...</span></p>
            </div>
        </div>
    </div>

    <script>
        // Update system info
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
        document.getElementById('serverInfo').textContent = `Node.js Server on ${window.location.host}`;

        // Check server status
        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            try {
                const response = await fetch('/');
                if (response.ok) {
                    statusDiv.className = 'status-card success';
                    statusDiv.innerHTML = '<h3>🖥️ Server Status</h3><p>✅ Server is running and accessible</p>';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.className = 'status-card error';
                statusDiv.innerHTML = `<h3>🖥️ Server Status</h3><p>❌ Server connection failed: ${error.message}</p>`;
            }
        }

        // Check API key status
        async function checkApiKeyStatus() {
            const statusDiv = document.getElementById('apiKeyStatus');
            const setupDiv = document.getElementById('setupInstructions');

            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                if (data.success && data.geminiService && data.geminiService.available) {
                    statusDiv.className = 'status-card success';
                    statusDiv.innerHTML = `
                        <h3>🔑 Gemini API Configuration</h3>
                        <p>✅ Gemini API key is configured and valid</p>
                        <p><strong>Service:</strong> Google Gemini 1.5 Flash</p>
                        <p><strong>Status:</strong> Ready for AI-powered horoscopes</p>
                    `;
                    setupDiv.style.display = 'none';
                } else if (data.success && data.geminiService && !data.geminiService.available) {
                    statusDiv.className = 'status-card error';
                    statusDiv.innerHTML = `
                        <h3>🔑 Gemini API Configuration</h3>
                        <p>❌ Gemini API key is configured but invalid</p>
                        <p><strong>Error:</strong> ${data.geminiService.error || 'API key validation failed'}</p>
                        <p><strong>Service:</strong> Running in fallback mode</p>
                    `;
                    setupDiv.style.display = 'block';
                } else {
                    statusDiv.className = 'status-card warning';
                    statusDiv.innerHTML = `
                        <h3>🔑 Gemini API Configuration</h3>
                        <p>⚠️ Gemini API key not configured</p>
                        <p><strong>Message:</strong> Please configure your Google Gemini API key</p>
                        <p><strong>Status:</strong> Using static fallback content</p>
                    `;
                    setupDiv.style.display = 'block';
                }
            } catch (error) {
                statusDiv.className = 'status-card error';
                statusDiv.innerHTML = `
                    <h3>🔑 Gemini API Configuration</h3>
                    <p>❌ Failed to check API status</p>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        // Test API functionality
        async function testApiKey() {
            const btn = document.getElementById('testApiBtn');
            const statusDiv = document.getElementById('apiTestStatus');

            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span> Testing...';

            try {
                const response = await fetch('/api/daily-horoscope/mesha');
                const data = await response.json();

                if (response.ok && data.success) {
                    statusDiv.className = 'status-card success';
                    statusDiv.innerHTML = `
                        <h3>🧪 Gemini API Functionality Test</h3>
                        <p>✅ Gemini API test successful!</p>
                        <p><strong>Service:</strong> Google Gemini 1.5 Flash</p>
                        <p><strong>Test Sign:</strong> මේෂ (Mesha)</p>
                        <p><strong>Response Time:</strong> ${data.metadata?.responseTime || 'Unknown'}</p>
                        <p><strong>Service Available:</strong> ${data.metadata?.serviceAvailable ? 'Yes' : 'Fallback Mode'}</p>
                        <button id="testApiBtn" class="btn success">Test Passed ✓</button>
                    `;
                } else {
                    throw new Error(data.error || 'API test failed');
                }
            } catch (error) {
                statusDiv.className = 'status-card error';
                statusDiv.innerHTML = `
                    <h3>🧪 Gemini API Functionality Test</h3>
                    <p>❌ API test failed</p>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Note:</strong> Service may be running in fallback mode</p>
                    <button id="testApiBtn" class="btn">Retry Test</button>
                `;
            }

            // Re-attach event listener
            const newBtn = document.getElementById('testApiBtn');
            if (newBtn) {
                newBtn.disabled = false;
                newBtn.addEventListener('click', testApiKey);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkServerStatus();
            checkApiKeyStatus();
            
            document.getElementById('testApiBtn').addEventListener('click', testApiKey);
            
            // Auto-refresh every 30 seconds
            setInterval(() => {
                checkServerStatus();
                checkApiKeyStatus();
            }, 30000);
        });
    </script>
</body>
</html>
